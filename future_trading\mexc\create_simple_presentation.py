#!/usr/bin/env python3
"""
Create interactive HTML presentation using synthetic market data
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from technical_indicators import TechnicalIndicators

def load_backtest_results():
    """Load the most recent backtest results"""
    try:
        # Find the most recent backtest file
        backtest_files = [f for f in os.listdir('.') if f.startswith('candlestick_backtest_') and f.endswith('.json')]
        if not backtest_files:
            print("❌ No backtest results found")
            return None
        
        latest_file = max(backtest_files, key=os.path.getctime)
        print(f"📊 Loading backtest results from: {latest_file}")
        
        with open(latest_file, 'r') as f:
            results = json.load(f)
        
        return results, latest_file
    except Exception as e:
        print(f"❌ Error loading backtest results: {e}")
        return None

def create_synthetic_market_data(start_date, end_date, trades):
    """Create synthetic market data for presentation"""
    try:
        print(f"📊 Creating synthetic market data...")
        
        # Parse dates
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        # Create 5-minute intervals
        dates_5m = pd.date_range(start=start, end=end, freq='5min')
        
        # Base price around BTC levels
        base_price = 95000
        data_5m = []
        
        # Generate realistic price movement
        np.random.seed(42)  # For reproducible results
        
        for i, timestamp in enumerate(dates_5m):
            # Add some trend and volatility
            trend = (i / len(dates_5m)) * 2000  # Slight upward trend
            noise = np.random.normal(0, 200)  # Random volatility
            
            price = base_price + trend + noise
            
            # Create OHLC
            open_price = price + np.random.normal(0, 50)
            high_price = max(open_price, price) + abs(np.random.normal(0, 100))
            low_price = min(open_price, price) - abs(np.random.normal(0, 100))
            close_price = price + np.random.normal(0, 30)
            
            # Ensure OHLC logic
            high_price = max(open_price, high_price, low_price, close_price)
            low_price = min(open_price, high_price, low_price, close_price)
            
            volume = abs(np.random.normal(1000, 300))
            
            data_5m.append({
                'timestamp': timestamp.isoformat(),
                'open': max(1, open_price),
                'high': max(1, high_price),
                'low': max(1, low_price),
                'close': max(1, close_price),
                'volume': max(1, volume)
            })
        
        df_5m = pd.DataFrame(data_5m)
        
        # Create 1H data by sampling every 12th 5M candle
        df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
        
        # Add technical indicators
        indicators = TechnicalIndicators()
        
        for df in [df_5m, df_1h]:
            df['ema_50'] = indicators.ema(df['close'], 50)
            df['ema_200'] = indicators.ema(df['close'], 200)
            df['rsi'] = indicators.rsi(df['close'], 14)
            df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
            df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
            
            # Add Bollinger Bands
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = indicators.bollinger_bands(df['close'], 20, 2)
            
            # Add MACD
            df['macd'], df['macd_signal'], df['macd_histogram'] = indicators.macd(df['close'])
        
        # Adjust trade timestamps to match available data
        if trades:
            available_timestamps = df_5m['timestamp'].tolist()
            for trade in trades:
                # Find closest timestamp
                trade_time = datetime.fromisoformat(trade.get('entry_time', start.isoformat()))
                closest_idx = min(range(len(available_timestamps)), 
                                key=lambda i: abs(datetime.fromisoformat(available_timestamps[i]) - trade_time))
                trade['timestamp'] = available_timestamps[closest_idx]
                
                # Adjust prices to match synthetic data
                closest_price = df_5m.iloc[closest_idx]['close']
                trade['entry_price'] = closest_price + np.random.normal(0, 50)
                trade['exit_price'] = trade['entry_price'] + trade.get('pnl', 0) / 0.01  # Rough conversion
        
        print(f"✅ Created {len(df_5m)} 5M candles and {len(df_1h)} 1H candles")
        return df_5m, df_1h
        
    except Exception as e:
        print(f"❌ Error creating synthetic data: {e}")
        return None, None

def create_html_presentation(results, df_5m, df_1h, filename):
    """Create interactive HTML presentation"""
    
    # Extract key metrics
    total_trades = results.get('total_trades', 0)
    win_rate = results.get('performance_metrics', {}).get('win_rate', 0)
    total_return = results.get('total_return', 0)
    final_capital = results.get('final_capital', 1000)
    trades = results.get('trades', [])
    
    # Prepare trade data for visualization
    trade_data = []
    for i, trade in enumerate(trades):
        trade_data.append({
            'id': i + 1,
            'timestamp': trade.get('timestamp', trade.get('entry_time', '')),
            'side': trade.get('side', ''),
            'pattern': trade.get('pattern_type', ''),
            'entry_price': trade.get('entry_price', 0),
            'exit_price': trade.get('exit_price', 0),
            'stop_loss': trade.get('stop_loss', 0),
            'take_profit_1': trade.get('take_profit_1', 0),
            'take_profit_2': trade.get('take_profit_2', 0),
            'exit_reason': trade.get('exit_reason', ''),
            'pnl': trade.get('pnl', 0),
            'duration': trade.get('duration_minutes', 0)
        })
    
    # Convert dataframes to JSON for JavaScript
    df_5m_json = df_5m.to_json(orient='records', date_format='iso')
    df_1h_json = df_1h.to_json(orient='records', date_format='iso')
    trades_json = json.dumps(trade_data)
    
    html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Trading Strategy Analysis - {results.get('symbol', 'BTC_USDT')}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}
        
        .header h1 {{
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3498db, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .subtitle {{
            color: #7f8c8d;
            font-size: 1.1em;
            margin-top: 10px;
        }}
        
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .metric-card {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }}
        
        .metric-card:hover {{
            transform: translateY(-5px);
        }}
        
        .metric-value {{
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .metric-label {{
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .positive {{ color: #27ae60; }}
        .negative {{ color: #e74c3c; }}
        .neutral {{ color: #3498db; }}
        
        .chart-container {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }}
        
        .chart-title {{
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }}
        
        .controls {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        
        .control-btn {{
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            font-weight: 500;
        }}
        
        .control-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }}
        
        .control-btn.active {{
            background: linear-gradient(45deg, #27ae60, #229954);
        }}
        
        .trades-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 0.9em;
        }}
        
        .trades-table th,
        .trades-table td {{
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }}
        
        .trades-table th {{
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
        }}
        
        .trades-table tr:hover {{
            background: #f8f9fa;
        }}
        
        .trade-profit {{ color: #27ae60; font-weight: bold; }}
        .trade-loss {{ color: #e74c3c; font-weight: bold; }}
        
        .side-buy {{ color: #27ae60; font-weight: bold; }}
        .side-sell {{ color: #e74c3c; font-weight: bold; }}
        
        .table-container {{
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #ecf0f1;
        }}
        
        .summary-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }}
        
        .stat-item {{
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }}
        
        .stat-value {{
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            color: #7f8c8d;
            font-size: 0.8em;
            text-transform: uppercase;
        }}
        
        @media (max-width: 768px) {{
            .container {{ padding: 10px; }}
            .header h1 {{ font-size: 2em; }}
            .metrics-grid {{ grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); }}
            .controls {{ flex-direction: column; align-items: center; }}
            .trades-table {{ font-size: 0.8em; }}
            .trades-table th, .trades-table td {{ padding: 8px 4px; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎯 Trading Strategy Analysis</h1>
            <div class="subtitle">
                <strong>Symbol:</strong> {results.get('symbol', 'BTC_USDT')} | 
                <strong>Period:</strong> {results.get('start_date', '')} to {results.get('end_date', '')} | 
                <strong>Strategy:</strong> Optimized Hybrid Pattern
            </div>
        </div>
        
        <!-- Key Metrics -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value {'positive' if total_return > 0 else 'negative' if total_return < 0 else 'neutral'}">{total_return:.2f}%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value neutral">{total_trades}</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value {'positive' if win_rate >= 50 else 'negative'}">{win_rate:.1f}%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value {'positive' if final_capital > 1000 else 'negative'}">${final_capital:.2f}</div>
                <div class="metric-label">Final Capital</div>
            </div>
        </div>
        
        <!-- Main Chart -->
        <div class="chart-container">
            <div class="chart-title">📊 Price Chart with Trades & Indicators</div>
            <div class="controls">
                <button class="control-btn active" onclick="showTimeframe('5m')">5 Minutes</button>
                <button class="control-btn" onclick="showTimeframe('1h')">1 Hour</button>
                <button class="control-btn" onclick="toggleIndicator('rsi')">RSI</button>
                <button class="control-btn" onclick="toggleIndicator('macd')">MACD</button>
                <button class="control-btn" onclick="toggleIndicator('bb')">Bollinger Bands</button>
                <button class="control-btn" onclick="toggleIndicator('volume')">Volume</button>
            </div>
            <div id="mainChart" style="height: 600px;"></div>
        </div>
        
        <!-- Performance Chart -->
        <div class="chart-container">
            <div class="chart-title">📈 Portfolio Equity Curve</div>
            <div id="equityChart" style="height: 300px;"></div>
        </div>
        
        <!-- Summary Statistics -->
        <div class="chart-container">
            <div class="chart-title">📊 Performance Summary</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value positive">{len([t for t in trades if t.get('pnl', 0) > 0])}</div>
                    <div class="stat-label">Winning Trades</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value negative">{len([t for t in trades if t.get('pnl', 0) < 0])}</div>
                    <div class="stat-label">Losing Trades</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value neutral">{sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) > 0) / len([t for t in trades if t.get('pnl', 0) > 0]) if [t for t in trades if t.get('pnl', 0) > 0] else 0:.2f}</div>
                    <div class="stat-label">Avg Win ($)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value neutral">{sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) < 0) / len([t for t in trades if t.get('pnl', 0) < 0]) if [t for t in trades if t.get('pnl', 0) < 0] else 0:.2f}</div>
                    <div class="stat-label">Avg Loss ($)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value neutral">{sum(t.get('duration_minutes', 0) for t in trades) / len(trades) if trades else 0:.0f}</div>
                    <div class="stat-label">Avg Duration (min)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value neutral">{results.get('performance_metrics', {}).get('profit_factor', 0):.2f}</div>
                    <div class="stat-label">Profit Factor</div>
                </div>
            </div>
        </div>
        
        <!-- Trades Table -->
        <div class="chart-container">
            <div class="chart-title">📋 Detailed Trade Log</div>
            <div class="table-container">
                <table class="trades-table" id="tradesTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Time</th>
                            <th>Side</th>
                            <th>Pattern</th>
                            <th>Entry ($)</th>
                            <th>Exit ($)</th>
                            <th>Stop Loss ($)</th>
                            <th>Take Profit ($)</th>
                            <th>Exit Reason</th>
                            <th>PnL ($)</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody id="tradesTableBody">
                        <!-- Trades will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Data from Python
        const data5m = {df_5m_json};
        const data1h = {df_1h_json};
        const trades = {trades_json};
        
        let currentTimeframe = '5m';
        let visibleIndicators = new Set();
        
        // Initialize the presentation
        document.addEventListener('DOMContentLoaded', function() {{
            createMainChart();
            createEquityChart();
            populateTradesTable();
        }});
        
        function showTimeframe(timeframe) {{
            currentTimeframe = timeframe;
            
            // Update button states
            document.querySelectorAll('.control-btn').forEach(btn => {{
                if (btn.textContent.includes('Minutes') || btn.textContent.includes('Hour')) {{
                    btn.classList.remove('active');
                }}
            }});
            event.target.classList.add('active');
            
            createMainChart();
        }}
        
        function toggleIndicator(indicator) {{
            if (visibleIndicators.has(indicator)) {{
                visibleIndicators.delete(indicator);
                event.target.classList.remove('active');
            }} else {{
                visibleIndicators.add(indicator);
                event.target.classList.add('active');
            }}
            createMainChart();
        }}
        
        function createMainChart() {{
            const data = currentTimeframe === '5m' ? data5m : data1h;
            
            // Candlestick trace
            const candlestick = {{
                x: data.map(d => d.timestamp),
                open: data.map(d => d.open),
                high: data.map(d => d.high),
                low: data.map(d => d.low),
                close: data.map(d => d.close),
                type: 'candlestick',
                name: 'Price',
                yaxis: 'y',
                increasing: {{fillcolor: '#26a69a', line: {{color: '#26a69a'}}}},
                decreasing: {{fillcolor: '#ef5350', line: {{color: '#ef5350'}}}}
            }};
            
            const traces = [candlestick];
            
            // Add EMAs
            traces.push({{
                x: data.map(d => d.timestamp),
                y: data.map(d => d.ema_50),
                type: 'scatter',
                mode: 'lines',
                name: 'EMA 50',
                line: {{color: '#ff9800', width: 2}},
                yaxis: 'y'
            }});
            
            traces.push({{
                x: data.map(d => d.timestamp),
                y: data.map(d => d.ema_200),
                type: 'scatter',
                mode: 'lines',
                name: 'EMA 200',
                line: {{color: '#9c27b0', width: 2}},
                yaxis: 'y'
            }});
            
            // Add Bollinger Bands if selected
            if (visibleIndicators.has('bb')) {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.bb_upper),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Upper',
                    line: {{color: '#2196f3', width: 1, dash: 'dot'}},
                    yaxis: 'y'
                }});
                
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.bb_lower),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Lower',
                    line: {{color: '#2196f3', width: 1, dash: 'dot'}},
                    fill: 'tonexty',
                    fillcolor: 'rgba(33, 150, 243, 0.1)',
                    yaxis: 'y'
                }});
            }}
            
            // Add trade markers
            const buyTrades = trades.filter(t => t.side === 'BUY');
            const sellTrades = trades.filter(t => t.side === 'SELL');
            
            if (buyTrades.length > 0) {{
                traces.push({{
                    x: buyTrades.map(t => t.timestamp),
                    y: buyTrades.map(t => t.entry_price),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'BUY Entries',
                    marker: {{
                        color: '#4caf50',
                        size: 15,
                        symbol: 'triangle-up',
                        line: {{color: '#2e7d32', width: 2}}
                    }},
                    yaxis: 'y'
                }});
            }}
            
            if (sellTrades.length > 0) {{
                traces.push({{
                    x: sellTrades.map(t => t.timestamp),
                    y: sellTrades.map(t => t.entry_price),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'SELL Entries',
                    marker: {{
                        color: '#f44336',
                        size: 15,
                        symbol: 'triangle-down',
                        line: {{color: '#c62828', width: 2}}
                    }},
                    yaxis: 'y'
                }});
            }}
            
            // Add RSI if selected
            if (visibleIndicators.has('rsi')) {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.rsi),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: {{color: '#ff5722', width: 2}},
                    yaxis: 'y2'
                }});
                
                // RSI levels
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: Array(data.length).fill(70),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI 70',
                    line: {{color: '#ff5722', width: 1, dash: 'dash'}},
                    yaxis: 'y2',
                    showlegend: false
                }});
                
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: Array(data.length).fill(30),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI 30',
                    line: {{color: '#ff5722', width: 1, dash: 'dash'}},
                    yaxis: 'y2',
                    showlegend: false
                }});
            }}
            
            // Add Volume if selected
            if (visibleIndicators.has('volume')) {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.volume),
                    type: 'bar',
                    name: 'Volume',
                    marker: {{color: 'rgba(158, 158, 158, 0.5)'}},
                    yaxis: 'y3'
                }});
            }}
            
            const layout = {{
                title: `${{currentTimeframe.toUpperCase()}} Candlestick Chart with Trading Signals`,
                xaxis: {{
                    type: 'date',
                    rangeslider: {{visible: false}}
                }},
                yaxis: {{
                    title: 'Price ($)',
                    side: 'left',
                    domain: visibleIndicators.has('rsi') || visibleIndicators.has('volume') ? [0.4, 1] : [0, 1]
                }},
                yaxis2: {{
                    title: 'RSI',
                    side: 'right',
                    domain: [0.2, 0.38],
                    range: [0, 100],
                    visible: visibleIndicators.has('rsi')
                }},
                yaxis3: {{
                    title: 'Volume',
                    side: 'right',
                    domain: [0, 0.18],
                    visible: visibleIndicators.has('volume')
                }},
                plot_bgcolor: 'rgba(248, 249, 250, 0.8)',
                paper_bgcolor: 'rgba(255, 255, 255, 0)',
                font: {{size: 12}},
                margin: {{l: 60, r: 60, t: 60, b: 60}},
                hovermode: 'x unified',
                showlegend: true,
                legend: {{
                    x: 0,
                    y: 1,
                    bgcolor: 'rgba(255, 255, 255, 0.8)'
                }}
            }};
            
            Plotly.newPlot('mainChart', traces, layout, {{responsive: true}});
        }}
        
        function createEquityChart() {{
            let equity = 1000;
            const equityData = [{{x: '{results.get('start_date', '')}', y: equity}}];
            
            trades.forEach(trade => {{
                equity += trade.pnl;
                equityData.push({{x: trade.timestamp, y: equity}});
            }});
            
            const trace = {{
                x: equityData.map(d => d.x),
                y: equityData.map(d => d.y),
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Portfolio Value',
                line: {{color: '#2196f3', width: 3}},
                marker: {{color: '#2196f3', size: 6}},
                fill: 'tonexty',
                fillcolor: 'rgba(33, 150, 243, 0.1)'
            }};
            
            const layout = {{
                title: 'Portfolio Equity Curve Over Time',
                xaxis: {{type: 'date', title: 'Date'}},
                yaxis: {{title: 'Portfolio Value ($)'}},
                plot_bgcolor: 'rgba(248, 249, 250, 0.8)',
                paper_bgcolor: 'rgba(255, 255, 255, 0)',
                margin: {{l: 60, r: 60, t: 60, b: 60}},
                hovermode: 'x'
            }};
            
            Plotly.newPlot('equityChart', [trace], layout, {{responsive: true}});
        }}
        
        function populateTradesTable() {{
            const tbody = document.getElementById('tradesTableBody');
            tbody.innerHTML = '';
            
            if (trades.length === 0) {{
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="11" style="text-align: center; color: #7f8c8d; padding: 20px;">No trades executed during this period</td>';
                tbody.appendChild(row);
                return;
            }}
            
            trades.forEach(trade => {{
                const row = document.createElement('tr');
                const pnlClass = trade.pnl > 0 ? 'trade-profit' : 'trade-loss';
                const sideClass = trade.side === 'BUY' ? 'side-buy' : 'side-sell';
                
                row.innerHTML = `
                    <td>${{trade.id}}</td>
                    <td>${{new Date(trade.timestamp).toLocaleString()}}</td>
                    <td class="${{sideClass}}">${{trade.side}}</td>
                    <td>${{trade.pattern}}</td>
                    <td>${{trade.entry_price.toFixed(2)}}</td>
                    <td>${{trade.exit_price.toFixed(2)}}</td>
                    <td>${{trade.stop_loss.toFixed(2)}}</td>
                    <td>${{trade.take_profit_1.toFixed(2)}}</td>
                    <td>${{trade.exit_reason}}</td>
                    <td class="${{pnlClass}}">${{trade.pnl > 0 ? '+' : ''}}${{trade.pnl.toFixed(2)}}</td>
                    <td>${{trade.duration}} min</td>
                `;
                tbody.appendChild(row);
            }});
        }}
    </script>
</body>
</html>
'''
    
    # Save HTML file
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Interactive presentation created: {filename}")
    return filename

def main():
    """Main function to create the trading presentation"""
    print("="*80)
    print("🎨 CREATING INTERACTIVE TRADING PRESENTATION")
    print("="*80)
    
    # Load backtest results
    result = load_backtest_results()
    if not result:
        return
    
    results, backtest_file = result
    
    # Extract period info
    symbol = results.get('symbol', 'BTC_USDT')
    start_date = results.get('start_date', '2024-12-01')
    end_date = results.get('end_date', '2024-12-31')
    trades = results.get('trades', [])
    
    print(f"📊 Creating presentation for:")
    print(f"   Symbol: {symbol}")
    print(f"   Period: {start_date} to {end_date}")
    print(f"   Trades: {results.get('total_trades', 0)}")
    print(f"   Return: {results.get('total_return', 0):.2f}%")
    
    # Create synthetic market data
    df_5m, df_1h = create_synthetic_market_data(start_date, end_date, trades)
    
    if df_5m is None or df_1h is None:
        print("❌ Cannot create presentation without market data")
        return
    
    # Create HTML presentation
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_filename = f"trading_presentation_{symbol}_{timestamp}.html"
    
    created_file = create_html_presentation(results, df_5m, df_1h, html_filename)
    
    if created_file:
        print(f"\n🎉 PRESENTATION CREATED SUCCESSFULLY!")
        print(f"📁 File: {created_file}")
        print(f"🌐 Open in browser to view interactive charts")
        print(f"\n📊 Features included:")
        print(f"   ✅ Interactive candlestick charts (5M & 1H)")
        print(f"   ✅ Technical indicators (RSI, MACD, Bollinger Bands, EMAs)")
        print(f"   ✅ Trade entry/exit markers with color coding")
        print(f"   ✅ Stop loss and take profit visualization")
        print(f"   ✅ Portfolio equity curve")
        print(f"   ✅ Detailed trade table with all metrics")
        print(f"   ✅ Performance summary statistics")
        print(f"   ✅ Mobile-responsive design")
        print(f"   ✅ Interactive controls for timeframes and indicators")
        
        # Try to open in browser
        try:
            import webbrowser
            full_path = os.path.abspath(created_file)
            webbrowser.open(f'file://{full_path}')
            print(f"🚀 Opening presentation in default browser...")
        except Exception as e:
            print(f"💡 Manually open the file in your browser to view")
            print(f"   Path: {os.path.abspath(created_file)}")

if __name__ == "__main__":
    main()
