#!/usr/bin/env python3
"""
Final test with ultra-relaxed parameters to verify trade generation
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG

def test_ultra_relaxed_final():
    """Final test with ultra-relaxed parameters"""
    print("="*80)
    print("🚀 FINAL TEST WITH ULTRA-RELAXED PARAMETERS")
    print("="*80)
    
    # Show ultra-relaxed configuration
    print("⚙️ ULTRA-RELAXED CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"   {key}: {value}")
    
    # Create test data with obvious patterns at the END
    dates = pd.date_range(start='2024-01-01', periods=300, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        price = base_price + i * 2 + np.random.normal(0, 20)
        
        # Create obvious patterns near the end for testing
        if i >= 290:  # Last 10 candles
            if i == 295:  # Hammer pattern
                open_price = price
                high_price = price + 30
                low_price = price - 200  # Long lower shadow
                close_price = price + 25
                volume = 2000
                print(f"   📍 Embedded HAMMER at index {i} (near end)")
            elif i == 297:  # Shooting star
                open_price = price
                high_price = price + 200  # Long upper shadow
                low_price = price - 30
                close_price = price - 25
                volume = 1800
                print(f"   📍 Embedded SHOOTING STAR at index {i} (near end)")
            else:
                open_price = price
                high_price = price + abs(np.random.normal(0, 25))
                low_price = price - abs(np.random.normal(0, 25))
                close_price = price + np.random.normal(0, 15)
                volume = abs(np.random.normal(1000, 200))
        else:
            # Normal candles
            open_price = price
            high_price = price + abs(np.random.normal(0, 25))
            low_price = price - abs(np.random.normal(0, 25))
            close_price = price + np.random.normal(0, 15)
            volume = abs(np.random.normal(1000, 200))
        
        # Ensure OHLC logic
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': max(0.01, open_price),
            'high': max(0.01, high_price),
            'low': max(0.01, low_price),
            'close': max(0.01, close_price),
            'volume': max(1, volume)
        })
    
    df_5m = pd.DataFrame(data)
    
    # Create 1H data
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    
    # Add indicators to both timeframes
    indicators = TechnicalIndicators()
    
    for df in [df_5m, df_1h]:
        df['ema_50'] = indicators.ema(df['close'], 50)
        df['ema_200'] = indicators.ema(df['close'], 200)
        df['rsi'] = indicators.rsi(df['close'], 14)
        df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
        
        macd_data = indicators.macd(df['close'])
        df['macd'] = macd_data['macd']
        df['macd_signal'] = macd_data['signal']
        df['macd_histogram'] = macd_data['histogram']
        
        df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
        df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    print(f"   Created {len(df_5m)} 5M candles and {len(df_1h)} 1H candles")
    
    # Test strategy signal generation
    strategy = CandlestickTradingStrategy()
    
    print(f"\n🎯 TESTING SIGNAL GENERATION:")
    
    # Test at the end where we have patterns
    signal_data = strategy.generate_trading_signal(df_1h, df_5m, 'BTC_USDT')
    
    print(f"   Signal: {signal_data.get('signal', 'None')}")
    print(f"   Confidence: {signal_data.get('confidence', 0):.3f}")
    print(f"   Reason: {signal_data.get('reason', 'N/A')}")
    
    if signal_data.get('signal'):
        print(f"   ✅ SUCCESS! Signal generated")
        print(f"   Pattern: {signal_data.get('pattern_type', 'unknown')}")
        print(f"   Pattern strength: {signal_data.get('pattern_strength', 0):.3f}")
        
        if signal_data.get('levels'):
            levels = signal_data['levels']
            print(f"   Entry: ${levels['entry_price']:.2f}")
            print(f"   Stop Loss: ${levels['stop_loss']:.2f}")
            print(f"   Take Profit 1: ${levels['take_profit_1']:.2f}")
            print(f"   Position Size: {levels['position_size']:.6f}")
    else:
        print(f"   ❌ Still no signal generated")
        
        # Debug the last few candles
        print(f"\n🔍 DEBUGGING LAST FEW CANDLES:")
        patterns = CandlestickPatterns()
        
        for i in range(-5, 0):  # Last 5 candles
            idx = len(df_5m) + i
            pattern_result = patterns.detect_all_patterns(df_5m, i)
            
            if pattern_result['has_pattern']:
                strongest = pattern_result['strongest_pattern']
                print(f"      Index {idx}: {strongest['name']} (strength: {strongest['strength']:.3f})")
            else:
                print(f"      Index {idx}: No pattern")
    
    # Test with a mini simulation
    print(f"\n🧪 MINI SIMULATION TEST:")
    signals_found = 0
    
    # Test every 10 candles in the last 100
    for i in range(200, len(df_5m), 10):
        df_5m_slice = df_5m.iloc[:i+1].copy()
        h1_index = min(i // 12, len(df_1h) - 1)
        df_1h_slice = df_1h.iloc[:h1_index+1].copy()
        
        if len(df_1h_slice) >= 20:  # Minimum data requirement
            signal_data = strategy.generate_trading_signal(df_1h_slice, df_5m_slice, 'BTC_USDT')
            
            if signal_data.get('signal'):
                signals_found += 1
                print(f"   Signal {signals_found} at index {i}: {signal_data['signal']} "
                      f"{signal_data.get('pattern_type', 'unknown')} "
                      f"(confidence: {signal_data['confidence']:.3f})")
                
                if signals_found >= 3:  # Limit output
                    break
    
    print(f"\n📊 SIMULATION RESULTS:")
    print(f"   Signals found: {signals_found}")
    print(f"   Test points: {len(range(200, len(df_5m), 10))}")
    
    if signals_found > 0:
        print(f"   ✅ SUCCESS! Ultra-relaxed parameters generate signals")
        print(f"   📈 Ready for full backtesting")
    else:
        print(f"   ⚠️ Still no signals - may need to debug pattern detection logic")
        
        # Final debug - check if patterns exist at all
        patterns = CandlestickPatterns()
        total_patterns = 0
        strong_patterns = 0
        
        for i in range(50, len(df_5m)):
            pattern_result = patterns.detect_all_patterns(df_5m, i)
            if pattern_result['has_pattern']:
                total_patterns += 1
                if pattern_result['max_strength'] >= CANDLESTICK_STRATEGY_CONFIG['pattern_strength_threshold']:
                    strong_patterns += 1
        
        print(f"\n🔍 FINAL PATTERN ANALYSIS:")
        print(f"   Total patterns: {total_patterns}")
        print(f"   Strong patterns: {strong_patterns}")
        print(f"   Pattern rate: {total_patterns / (len(df_5m) - 50) * 100:.1f}%")
        print(f"   Strong rate: {strong_patterns / (len(df_5m) - 50) * 100:.1f}%")

def main():
    """Main test function"""
    test_ultra_relaxed_final()
    
    print(f"\n" + "="*80)
    print(f"🎯 CONCLUSION:")
    print(f"="*80)
    print(f"If signals are generated above, the ultra-relaxed parameters work!")
    print(f"")
    print(f"🚀 READY FOR FULL BACKTEST:")
    print(f"   python run_candlestick_backtest.py --symbol BTC_USDT --start-date 2024-11-01 --end-date 2024-12-31 --verbose")
    print(f"")
    print(f"📊 Expected results with ultra-relaxed parameters:")
    print(f"   • More trades (potentially 10-50 trades per month)")
    print(f"   • Lower win rate (40-60%)")
    print(f"   • Good for testing and optimization")

if __name__ == "__main__":
    main()
