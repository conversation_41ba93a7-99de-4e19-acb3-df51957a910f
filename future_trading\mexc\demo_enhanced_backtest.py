#!/usr/bin/env python3
"""
Demo of enhanced backtest with relaxed parameters to show trades and logging
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Temporarily modify config for demo
import config
original_config = config.CANDLESTICK_STRATEGY_CONFIG.copy()

# Use very relaxed parameters for demo
config.CANDLESTICK_STRATEGY_CONFIG.update({
    'pattern_strength_threshold': 0.2,  # Very low threshold
    'volume_multiplier': 1.0,           # No volume requirement
    'rsi_filter_enabled': False,        # Disable RSI filter
    'support_resistance_buffer': 0.01,  # Large buffer
    'min_pattern_body_ratio': 0.1,     # Very low minimum
    'max_pattern_body_ratio': 1.0,     # No maximum
})

from run_candlestick_backtest import EnhancedCandlestickBacktestEngine
from test_enhanced_backtest import MockEnhancedBacktestEngine

def main():
    """Demo with relaxed parameters to show actual trades"""
    print("="*80)
    print("🎯 ENHANCED BACKTEST DEMO - WITH TRADES!")
    print("="*80)
    print("This demo uses relaxed parameters to generate actual trades")
    print("and showcase the enhanced logging and progress features.")
    print("="*80)
    
    # Show modified configuration
    print("\n⚙️  DEMO CONFIGURATION (Relaxed for more trades):")
    for key, value in config.CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"   {key}: {value}")
    
    print("\n🚀 Starting demo with relaxed parameters...")
    print("🎯 This should generate some trades to demonstrate the features!")
    print("-" * 80)
    
    # Initialize enhanced engine with verbose logging
    engine = MockEnhancedBacktestEngine(verbose=True)
    
    # Run backtest with longer period for more opportunities
    symbol = 'BTC_USDT'
    start_date = '2024-05-01'
    end_date = '2024-06-30'  # 2 months for more data
    
    print(f"\n📊 Running enhanced backtest for {symbol}")
    print(f"📅 Period: {start_date} to {end_date}")
    print("🔍 Watch for:")
    print("   • Real-time progress updates every 2%")
    print("   • Trade notifications when signals are found")
    print("   • Live performance tracking")
    print("   • Pattern-specific analysis")
    print("-" * 80)
    
    # Run the backtest
    results = engine.run_backtest(symbol, start_date, end_date)
    
    if 'error' in results:
        print(f"\n❌ Demo failed: {results['error']}")
        return
    
    # Show enhanced results display
    print(f"\n🎊 DEMO RESULTS:")
    
    if results['total_trades'] > 0:
        print(f"✅ SUCCESS! Generated {results['total_trades']} trades")
        print(f"💰 Final Return: {results['total_return']:.2f}%")
        print(f"🎯 Win Rate: {results['performance_metrics'].get('win_rate', 0):.1f}%")
        print(f"📊 Profit Factor: {results['performance_metrics'].get('profit_factor', 0):.2f}")
        
        # Show pattern breakdown
        if results.get('pattern_performance'):
            print(f"\n🎨 Pattern Breakdown:")
            for pattern, stats in results['pattern_performance'].items():
                print(f"   {pattern}: {stats['trades']} trades, {stats['win_rate']:.1f}% win rate")
    else:
        print(f"⚠️  Still no trades generated.")
        print(f"   The market conditions or pattern detection may be too strict.")
        print(f"   In a real backtest, you would:")
        print(f"   • Use longer time periods")
        print(f"   • Adjust pattern thresholds")
        print(f"   • Review market condition filters")
    
    print(f"\n✨ ENHANCED FEATURES DEMONSTRATED:")
    print(f"   ✅ Real-time progress tracking")
    print(f"   ✅ Live performance updates")
    print(f"   ✅ Trade-by-trade notifications")
    print(f"   ✅ Pattern performance analysis")
    print(f"   ✅ Detailed result formatting")
    print(f"   ✅ Emoji-enhanced console output")
    print(f"   ✅ JSON result export")
    
    print(f"\n🎯 TO USE WITH REAL DATA:")
    print(f"   1. Run: python run_candlestick_backtest.py --symbol BTC_USDT --verbose")
    print(f"   2. Add --analyze-patterns for detailed pattern analysis")
    print(f"   3. Use --optimize for parameter optimization")
    print(f"   4. Adjust CANDLESTICK_STRATEGY_CONFIG for your preferences")
    
    # Restore original config
    config.CANDLESTICK_STRATEGY_CONFIG = original_config
    
    print("\n" + "="*80)
    print("🎉 ENHANCED BACKTEST DEMO COMPLETED!")
    print("="*80)
    print("The enhanced run_candlestick_backtest.py now provides:")
    print("• 📊 Real-time progress updates during backtesting")
    print("• 🎯 Live trade notifications with pattern details")
    print("• 📈 Current win rate and performance tracking")
    print("• 🎨 Pattern-specific performance analysis")
    print("• ✨ Enhanced console output with emojis and formatting")
    print("• 💾 Detailed JSON result export")
    print("• ⚙️  Verbose logging option (--verbose flag)")

if __name__ == "__main__":
    main()
