2025-07-25 14:37:06 - root - INFO - setup_logging:68 - ================================================================================
2025-07-25 14:37:06 - root - INFO - setup_logging:69 - MEXC FUTURES TRADING BOT - NEW SESSION STARTED
2025-07-25 14:37:06 - root - INFO - setup_logging:70 - Log file: logs\mexc_futures_bot_20250725_143706.log
2025-07-25 14:37:06 - root - INFO - setup_logging:71 - Log level: INFO
2025-07-25 14:37:06 - root - INFO - setup_logging:72 - ================================================================================
2025-07-25 14:37:06 - mexc_api - INFO - __init__:21 - Initializing MEXC API client
2025-07-25 14:37:06 - mexc_api - INFO - __init__:22 - Base URL: https://contract.mexc.com
2025-07-25 14:37:06 - mexc_api - INFO - __init__:23 - API Key: mx0vglqq1w...2VIp
2025-07-25 14:37:06 - mexc_api - INFO - __init__:31 - MEXC API client initialized successfully
2025-07-25 14:37:06 - database - INFO - __init__:13 - DatabaseManager initialized
2025-07-25 14:37:06 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 14:37:06 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 14:37:06 - candlestick_trading_strategy - INFO - __init__:23 - Candlestick Trading Strategy initialized
2025-07-25 14:37:06 - candlestick_backtest_engine - INFO - __init__:36 - Candlestick Backtest Engine initialized
2025-07-25 14:37:06 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 14:37:06 - candlestick_trading_strategy - INFO - __init__:23 - Candlestick Trading Strategy initialized
2025-07-25 14:37:06 - candlestick_trading_strategy - INFO - __init__:34 - Hybrid Pattern Strategy initialized - Real patterns + guaranteed trades!
2025-07-25 14:37:06 - candlestick_backtest_engine - INFO - fetch_historical_data:41 - Fetching historical data for BTC_USDT from 2024-12-01 00:00:00 to 2024-12-31 00:00:00
2025-07-25 14:37:06 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-01 00:00:00 to 2024-12-31 00:00:00
2025-07-25 14:37:07 - candlestick_backtest_engine - INFO - fetch_historical_data:101 - Fetched 721 candles for BTC_USDT
2025-07-25 14:37:07 - candlestick_backtest_engine - INFO - fetch_historical_data:41 - Fetching historical data for BTC_USDT from 2024-12-01 00:00:00 to 2024-12-31 00:00:00
2025-07-25 14:37:07 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-01 00:00:00 to 2024-12-08 00:00:00
2025-07-25 14:37:07 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-08 00:00:00 to 2024-12-15 00:00:00
2025-07-25 14:37:07 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-15 00:00:00 to 2024-12-22 00:00:00
2025-07-25 14:37:08 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-22 00:00:00 to 2024-12-29 00:00:00
2025-07-25 14:37:08 - candlestick_backtest_engine - INFO - fetch_historical_data:70 - Fetching chunk: 2024-12-29 00:00:00 to 2024-12-31 00:00:00
2025-07-25 14:37:08 - candlestick_backtest_engine - INFO - fetch_historical_data:101 - Fetched 8580 candles for BTC_USDT
2025-07-25 14:37:08 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #1: BUY rsi_oversold_optimized at $95173.40
2025-07-25 14:37:08 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed BUY trade for BTC_USDT at 95182.91734
2025-07-25 14:37:08 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 1: take_profit_1 - PnL: 0.82
2025-07-25 14:37:09 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #3: SELL rsi_overbought_optimized at $96733.60
2025-07-25 14:37:09 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed SELL trade for BTC_USDT at 96723.92664
2025-07-25 14:37:09 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 2: stop_loss - PnL: -0.69
2025-07-25 14:37:11 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #5: BUY rsi_oversold_optimized at $100829.20
2025-07-25 14:37:11 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed BUY trade for BTC_USDT at 100839.28292
2025-07-25 14:37:11 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 3: stop_loss - PnL: -0.62
2025-07-25 14:37:14 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #11: SELL rsi_overbought_optimized at $100005.50
2025-07-25 14:37:14 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed SELL trade for BTC_USDT at 99995.49945
2025-07-25 14:37:14 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 4: take_profit_1 - PnL: 0.17
2025-07-25 14:37:17 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #16: SELL rsi_overbought_optimized at $104627.30
2025-07-25 14:37:17 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed SELL trade for BTC_USDT at 104616.83727
2025-07-25 14:37:17 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 5: stop_loss - PnL: -0.41
2025-07-25 14:37:19 - candlestick_trading_strategy - INFO - generate_fallback_trade:183 - 📊 FALLBACK TRADE #18: SELL rsi_overbought_optimized at $93918.10
2025-07-25 14:37:19 - candlestick_backtest_engine - INFO - execute_trade:208 - Executed SELL trade for BTC_USDT at 93908.70819
2025-07-25 14:37:19 - candlestick_backtest_engine - INFO - close_position:327 - Closed position 6: stop_loss - PnL: -1.27
