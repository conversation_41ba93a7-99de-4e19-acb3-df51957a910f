#!/usr/bin/env python3
"""
Analyze why the backtest generated no trades and suggest parameter adjustments
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG
from mexc_api import MEXCFuturesAPI

def analyze_no_trades_issue():
    """Analyze why no trades were generated"""
    print("="*80)
    print("🔍 ANALYZING WHY NO TRADES WERE GENERATED")
    print("="*80)
    
    # Load the backtest results
    try:
        with open('candlestick_backtest_BTC_USDT_20250725_133033.json', 'r') as f:
            results = json.load(f)
        print(f"✅ Loaded backtest results:")
        print(f"   Period: {results['start_date']} to {results['end_date']}")
        print(f"   Duration: {results['backtest_duration_seconds']:.1f} seconds")
        print(f"   Total trades: {results['total_trades']}")
    except FileNotFoundError:
        print("❌ Backtest results file not found")
        return
    
    print(f"\n📊 CURRENT CONFIGURATION ANALYSIS:")
    config = results['strategy_config']
    
    # Identify potential issues
    issues = []
    
    if config['pattern_strength_threshold'] >= 0.4:
        issues.append(f"❌ Pattern strength threshold too high: {config['pattern_strength_threshold']} (try 0.2-0.3)")
    
    if config['volume_multiplier'] >= 1.2:
        issues.append(f"❌ Volume multiplier too strict: {config['volume_multiplier']} (try 1.0-1.1)")
    
    if config['support_resistance_buffer'] <= 0.005:
        issues.append(f"❌ S/R buffer too tight: {config['support_resistance_buffer']} (try 0.01-0.02)")
    
    if config['min_pattern_body_ratio'] >= 0.2:
        issues.append(f"❌ Min body ratio too high: {config['min_pattern_body_ratio']} (try 0.1)")
    
    if config['max_pattern_body_ratio'] <= 0.9:
        issues.append(f"❌ Max body ratio too low: {config['max_pattern_body_ratio']} (try 1.0)")
    
    if issues:
        print(f"\n🚨 IDENTIFIED ISSUES:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print(f"\n✅ Configuration looks reasonable")
    
    # Test with sample data to see what's happening
    print(f"\n🧪 TESTING PATTERN DETECTION WITH SAMPLE DATA:")
    
    # Create a small sample of realistic data
    api = MEXCFuturesAPI()
    try:
        # Try to get some recent data for testing
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)  # Last week
        
        print(f"   Fetching recent data for testing...")
        kline_data = api.get_klines(
            symbol='BTC_USDT',
            interval='Min5',
            start_time=int(start_time.timestamp()),
            end_time=int(end_time.timestamp())
        )
        
        if kline_data and 'time' in kline_data:
            # Convert to DataFrame
            df = pd.DataFrame({
                'timestamp': pd.to_datetime(kline_data['time'], unit='s'),
                'open': pd.to_numeric(kline_data['open']),
                'high': pd.to_numeric(kline_data['high']),
                'low': pd.to_numeric(kline_data['low']),
                'close': pd.to_numeric(kline_data['close']),
                'volume': pd.to_numeric(kline_data['vol'])
            })
            
            # Add indicators
            indicators = TechnicalIndicators()
            df['ema_50'] = indicators.ema(df['close'], 50)
            df['ema_200'] = indicators.ema(df['close'], 200)
            df['rsi'] = indicators.rsi(df['close'], 14)
            df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
            df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
            
            print(f"   ✅ Got {len(df)} candles for analysis")
            
            # Test pattern detection
            patterns = CandlestickPatterns()
            pattern_count = 0
            strong_patterns = 0
            
            for i in range(50, len(df)):  # Start after enough data for indicators
                all_patterns = patterns.detect_all_patterns(df, i)
                if all_patterns['has_pattern']:
                    pattern_count += 1
                    if all_patterns['max_strength'] >= config['pattern_strength_threshold']:
                        strong_patterns += 1
            
            print(f"   📊 Pattern Analysis Results:")
            print(f"      Total patterns detected: {pattern_count}")
            print(f"      Strong patterns (>= {config['pattern_strength_threshold']}): {strong_patterns}")
            print(f"      Pattern detection rate: {pattern_count / (len(df) - 50) * 100:.1f}%")
            print(f"      Strong pattern rate: {strong_patterns / (len(df) - 50) * 100:.1f}%")
            
            if strong_patterns == 0:
                print(f"   ❌ No patterns meet the strength threshold!")
                print(f"      Recommendation: Lower pattern_strength_threshold to 0.2 or 0.3")
            
    except Exception as e:
        print(f"   ⚠️  Could not fetch real data for testing: {e}")
        print(f"   Using synthetic data for analysis...")
        
        # Create synthetic test data
        test_pattern_detection_synthetic()

def test_pattern_detection_synthetic():
    """Test pattern detection with synthetic data"""
    print(f"\n🧪 SYNTHETIC PATTERN DETECTION TEST:")
    
    # Create data with obvious patterns
    dates = pd.date_range(start='2024-01-01', periods=500, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        price = base_price + i * 2 + np.random.normal(0, 50)
        
        # Create obvious patterns every 100 candles
        if i % 100 == 50:  # Hammer
            open_price = price
            high_price = price + 30
            low_price = price - 200  # Long lower shadow
            close_price = price + 20
            volume = 2000
        elif i % 100 == 75:  # Shooting star
            open_price = price
            high_price = price + 200  # Long upper shadow
            low_price = price - 30
            close_price = price - 20
            volume = 1800
        else:  # Normal candle
            open_price = price
            high_price = price + abs(np.random.normal(0, 40))
            low_price = price - abs(np.random.normal(0, 40))
            close_price = price + np.random.normal(0, 20)
            volume = abs(np.random.normal(1000, 200))
        
        # Ensure OHLC logic
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    
    # Add indicators
    indicators = TechnicalIndicators()
    df['ema_50'] = indicators.ema(df['close'], 50)
    df['ema_200'] = indicators.ema(df['close'], 200)
    df['rsi'] = indicators.rsi(df['close'], 14)
    df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
    df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
    df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    # Test pattern detection
    patterns = CandlestickPatterns()
    
    print(f"   Testing with {len(df)} synthetic candles...")
    
    # Test at known pattern locations
    test_indices = [50, 75, 150, 175, 250, 275]
    detected_patterns = 0
    
    for idx in test_indices:
        if idx < len(df):
            all_patterns = patterns.detect_all_patterns(df, idx)
            if all_patterns['has_pattern']:
                detected_patterns += 1
                strongest = all_patterns['strongest_pattern']
                print(f"      Index {idx}: {strongest['name']} (strength: {strongest['strength']:.3f})")
            else:
                print(f"      Index {idx}: No pattern detected")
    
    print(f"   📊 Synthetic Test Results:")
    print(f"      Patterns detected: {detected_patterns}/{len(test_indices)}")
    print(f"      Detection rate: {detected_patterns / len(test_indices) * 100:.1f}%")

def suggest_optimized_parameters():
    """Suggest optimized parameters for better trade generation"""
    print(f"\n🎯 SUGGESTED PARAMETER ADJUSTMENTS:")
    print(f"="*60)
    
    print(f"📉 RELAXED PARAMETERS (More trades, potentially lower quality):")
    relaxed_config = {
        'pattern_strength_threshold': 0.2,    # Much lower threshold
        'volume_multiplier': 1.0,             # No volume requirement
        'support_resistance_buffer': 0.02,    # Larger buffer
        'min_pattern_body_ratio': 0.1,        # Lower minimum
        'max_pattern_body_ratio': 1.0,        # No maximum
        'rsi_filter_enabled': False,          # Keep disabled
    }
    
    for key, value in relaxed_config.items():
        print(f"   {key}: {value}")
    
    print(f"\n⚖️  BALANCED PARAMETERS (Moderate trades, good quality):")
    balanced_config = {
        'pattern_strength_threshold': 0.3,    # Moderate threshold
        'volume_multiplier': 1.1,             # Slight volume requirement
        'support_resistance_buffer': 0.01,    # Moderate buffer
        'min_pattern_body_ratio': 0.15,       # Moderate minimum
        'max_pattern_body_ratio': 0.95,       # High maximum
        'rsi_filter_enabled': False,          # Keep disabled initially
    }
    
    for key, value in balanced_config.items():
        print(f"   {key}: {value}")
    
    print(f"\n🎯 CONSERVATIVE PARAMETERS (Fewer trades, higher quality):")
    conservative_config = {
        'pattern_strength_threshold': 0.5,    # Higher threshold
        'volume_multiplier': 1.3,             # Higher volume requirement
        'support_resistance_buffer': 0.005,   # Tight buffer
        'min_pattern_body_ratio': 0.25,       # Higher minimum
        'max_pattern_body_ratio': 0.8,        # Lower maximum
        'rsi_filter_enabled': True,           # Enable RSI filter
    }
    
    for key, value in conservative_config.items():
        print(f"   {key}: {value}")

def main():
    """Main analysis function"""
    analyze_no_trades_issue()
    suggest_optimized_parameters()
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"="*60)
    print(f"1. 🎯 Start with RELAXED parameters to ensure trades are generated")
    print(f"2. 📊 Run a shorter backtest (1-2 months) to test quickly")
    print(f"3. 🔧 Gradually tighten parameters based on results")
    print(f"4. 📈 Use --verbose flag to see real-time pattern detection")
    print(f"5. 🧪 Test with recent data first before long historical periods")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Update config.py with relaxed parameters")
    print(f"   2. Run: python run_candlestick_backtest.py --symbol BTC_USDT --start-date 2024-11-01 --end-date 2024-12-31 --verbose")
    print(f"   3. Observe trade generation and adjust as needed")

if __name__ == "__main__":
    main()
