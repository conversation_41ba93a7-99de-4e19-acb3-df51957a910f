#!/usr/bin/env python3
"""
Debug version to see why signals aren't being generated
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG

def create_debug_data():
    """Create data with obvious patterns"""
    print("Creating debug data with obvious patterns...")
    
    dates = pd.date_range(start='2024-01-01', periods=300, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        price = base_price + i * 2
        
        # Create obvious hammer at index 100
        if i == 100:
            open_price = price
            high_price = price + 20
            low_price = price - 300  # Very long lower shadow
            close_price = price + 15
            volume = 2000  # High volume
            print(f"  Created HAMMER at index {i}: O={open_price:.2f}, H={high_price:.2f}, L={low_price:.2f}, C={close_price:.2f}")
            
        # Create obvious shooting star at index 150
        elif i == 150:
            open_price = price
            high_price = price + 300  # Very long upper shadow
            low_price = price - 20
            close_price = price - 15
            volume = 1800  # High volume
            print(f"  Created SHOOTING STAR at index {i}: O={open_price:.2f}, H={high_price:.2f}, L={low_price:.2f}, C={close_price:.2f}")
            
        # Create obvious doji at index 200
        elif i == 200:
            open_price = price
            high_price = price + 100
            low_price = price - 100
            close_price = price + 2  # Very small body
            volume = 1500
            print(f"  Created DOJI at index {i}: O={open_price:.2f}, H={high_price:.2f}, L={low_price:.2f}, C={close_price:.2f}")
            
        else:
            # Normal candle
            open_price = price
            high_price = price + abs(np.random.normal(0, 30))
            low_price = price - abs(np.random.normal(0, 30))
            close_price = price + np.random.normal(0, 15)
            volume = abs(np.random.normal(1000, 200))
        
        # Ensure OHLC logic
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': max(0.01, open_price),
            'high': max(0.01, high_price),
            'low': max(0.01, low_price),
            'close': max(0.01, close_price),
            'volume': max(1, volume)
        })
    
    df = pd.DataFrame(data)
    print(f"Created {len(df)} candles with obvious patterns")
    return df

def add_indicators_debug(df):
    """Add indicators with debug info"""
    print("Adding indicators...")
    
    indicators = TechnicalIndicators()
    
    df['ema_50'] = indicators.ema(df['close'], 50)
    df['ema_200'] = indicators.ema(df['close'], 200)
    df['rsi'] = indicators.rsi(df['close'], 14)
    df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
    
    macd_data = indicators.macd(df['close'])
    df['macd'] = macd_data['macd']
    df['macd_signal'] = macd_data['signal']
    df['macd_histogram'] = macd_data['histogram']
    
    df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
    df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    print("Indicators added")
    return df

def debug_pattern_detection():
    """Debug pattern detection at specific indices"""
    print("\n=== DEBUGGING PATTERN DETECTION ===")
    
    df = create_debug_data()
    df = add_indicators_debug(df)
    
    patterns = CandlestickPatterns()
    
    # Test at known pattern locations
    test_indices = [100, 150, 200]
    
    for idx in test_indices:
        print(f"\nDebugging patterns at index {idx}:")
        
        candle = df.iloc[idx]
        print(f"  Candle data: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")
        print(f"  Volume: {candle['volume']:.0f}, Volume ratio: {candle.get('volume_ratio', 0):.2f}")
        print(f"  RSI: {candle.get('rsi', 0):.1f}, ATR: {candle.get('atr', 0):.2f}")
        
        # Test individual patterns
        hammer = patterns.detect_hammer(df, idx)
        shooting_star = patterns.detect_shooting_star(df, idx)
        doji = patterns.detect_doji(df, idx)
        
        print(f"  Hammer: detected={hammer['detected']}, strength={hammer['strength']:.3f}")
        print(f"  Shooting Star: detected={shooting_star['detected']}, strength={shooting_star['strength']:.3f}")
        print(f"  Doji: detected={doji['detected']}, strength={doji['strength']:.3f}")
        
        # Test combined detection
        all_patterns = patterns.detect_all_patterns(df, idx)
        print(f"  Has pattern: {all_patterns['has_pattern']}")
        if all_patterns['has_pattern']:
            strongest = all_patterns['strongest_pattern']
            print(f"  Strongest: {strongest['name']} ({strongest['signal']}) - {strongest['strength']:.3f}")

def debug_strategy_conditions():
    """Debug strategy market conditions"""
    print("\n=== DEBUGGING STRATEGY CONDITIONS ===")
    
    df_5m = create_debug_data()
    df_5m = add_indicators_debug(df_5m)
    
    # Create 1H data
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    df_1h = add_indicators_debug(df_1h)
    
    print(f"5M data: {len(df_5m)} candles")
    print(f"1H data: {len(df_1h)} candles")
    
    strategy = CandlestickTradingStrategy()
    
    # Test market conditions
    print("\nTesting market conditions:")
    conditions = strategy.validate_market_conditions(df_1h, df_5m)
    print(f"  Valid: {conditions.get('valid', False)}")
    print(f"  Reason: {conditions.get('reason', 'N/A')}")
    
    if conditions.get('valid'):
        print(f"  Trend: {conditions.get('trend_direction', 'unknown')}")
        print(f"  Strength: {conditions.get('trend_strength', 0):.3f}")
        print(f"  Volatility ratio: {conditions.get('volatility_ratio', 0):.4f}")
        print(f"  Volume ratio: {conditions.get('volume_ratio', 0):.2f}")
    
    # Test at pattern locations
    test_indices = [100, 150, 200]
    
    for idx in test_indices:
        if idx < len(df_5m):
            print(f"\nTesting signal generation at index {idx}:")
            
            df_5m_slice = df_5m.iloc[:idx+1].copy()
            h1_index = min(idx // 12, len(df_1h) - 1)
            df_1h_slice = df_1h.iloc[:h1_index+1].copy()
            
            print(f"  Using {len(df_5m_slice)} 5M candles and {len(df_1h_slice)} 1H candles")
            
            signal_data = strategy.generate_trading_signal(df_1h_slice, df_5m_slice, 'BTC_USDT')
            
            print(f"  Signal: {signal_data.get('signal', 'None')}")
            print(f"  Confidence: {signal_data.get('confidence', 0):.3f}")
            print(f"  Reason: {signal_data.get('reason', 'N/A')}")
            
            if signal_data.get('pattern_type'):
                print(f"  Pattern: {signal_data['pattern_type']}")
                print(f"  Pattern strength: {signal_data.get('pattern_strength', 0):.3f}")

def debug_config():
    """Show current configuration"""
    print("\n=== CURRENT CONFIGURATION ===")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"  {key}: {value}")

def main():
    """Main debug function"""
    print("="*80)
    print("CANDLESTICK STRATEGY DEBUG")
    print("="*80)
    
    try:
        debug_config()
        debug_pattern_detection()
        debug_strategy_conditions()
        
        print("\n" + "="*80)
        print("DEBUG COMPLETED")
        print("="*80)
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
