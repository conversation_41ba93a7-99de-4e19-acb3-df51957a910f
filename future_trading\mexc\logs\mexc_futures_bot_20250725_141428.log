2025-07-25 14:14:28 - root - INFO - setup_logging:68 - ================================================================================
2025-07-25 14:14:28 - root - INFO - setup_logging:69 - MEXC FUTURES TRADING BOT - NEW SESSION STARTED
2025-07-25 14:14:28 - root - INFO - setup_logging:70 - Log file: logs\mexc_futures_bot_20250725_141428.log
2025-07-25 14:14:28 - root - INFO - setup_logging:71 - Log level: INFO
2025-07-25 14:14:28 - root - INFO - setup_logging:72 - ================================================================================
2025-07-25 14:14:28 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - __init__:23 - Candlestick Trading Strategy initialized
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - __init__:27 - Hybrid Pattern Strategy initialized - Real patterns + guaranteed trades!
2025-07-25 14:14:28 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - generate_fallback_trade:165 - 📊 FALLBACK TRADE #1: BUY fallback_alternate at $50257.03
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - generate_fallback_trade:165 - 📊 FALLBACK TRADE #2: SELL fallback_alternate at $50360.08
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - generate_fallback_trade:165 - 📊 FALLBACK TRADE #3: BUY fallback_alternate at $50592.36
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - INFO - generate_fallback_trade:165 - 📊 FALLBACK TRADE #4: SELL fallback_alternate at $50916.28
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
2025-07-25 14:14:28 - candlestick_trading_strategy - ERROR - generate_trading_signal:105 - Error in hybrid signal generation: 'pattern_count'
