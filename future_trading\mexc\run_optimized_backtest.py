#!/usr/bin/env python3
"""
Run backtest with optimized hybrid strategy based on loss analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_candlestick_backtest import EnhancedCandlestickBacktestEngine
from hybrid_pattern_strategy import HybridPatternStrategy
from config import CANDLESTICK_STRATEGY_CONFIG

class OptimizedBacktestEngine(EnhancedCandlestickBacktestEngine):
    """Backtest engine with optimized hybrid strategy"""
    
    def __init__(self, verbose=False):
        super().__init__(verbose)
        # Use optimized hybrid strategy
        self.strategy = HybridPatternStrategy()
        self.print_progress("🎯 Using OPTIMIZED HYBRID strategy - based on loss analysis!", force=True)

def main():
    """Run optimized backtest"""
    print("="*80)
    print("🎯 OPTIMIZED HYBRID STRATEGY BACKTEST")
    print("="*80)
    print("Based on analysis of losing trades, this optimized strategy features:")
    print("• Wider stop losses (2.0x ATR vs 1.5x) - reduce false exits")
    print("• Higher take profits (3.0x/5.0x vs 2.5x/4.0x) - better risk/reward")
    print("• Stronger trend requirements (2% vs 1%) - fewer false signals")
    print("• More extreme RSI thresholds (30/70 vs 40/60) - higher quality")
    print("• Less frequent trading (100-300 vs 50-200 candles) - quality over quantity")
    print("="*80)
    
    # Show optimized configuration
    print("\n⚙️ OPTIMIZED CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        if key in ['atr_multiplier_sl', 'atr_multiplier_tp1', 'atr_multiplier_tp2']:
            print(f"   {key}: {value} ✨ OPTIMIZED")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n🎯 OPTIMIZATION TARGETS:")
    print(f"   📈 Win Rate: 53.7% → 65%+ (improve by 20%)")
    print(f"   💰 Risk/Reward: 0.57:1 → 2.0:1+ (improve by 250%)")
    print(f"   📊 Trade Frequency: 41 → ~20 trades/month (reduce by 50%)")
    print(f"   🎯 Stop Loss Rate: 43.9% → 25% (reduce by 40%)")
    print(f"   📈 Total Return: -0.58% → +2%+ (turn profitable)")
    
    # Initialize optimized engine
    engine = OptimizedBacktestEngine(verbose=True)
    
    # Run backtest on same period for comparison
    symbol = 'BTC_USDT'
    start_date = '2024-12-01'
    end_date = '2024-12-31'
    
    print(f"\n📊 Running OPTIMIZED backtest:")
    print(f"   Symbol: {symbol}")
    print(f"   Period: {start_date} to {end_date} (same as original)")
    print(f"   Expected: ~20 trades (vs 41 original)")
    print("-" * 80)
    
    # Run the backtest
    results = engine.run_backtest(symbol, start_date, end_date)
    
    if 'error' in results:
        print(f"\n❌ Optimized backtest failed: {results['error']}")
        return
    
    # Compare with original results
    original_results = {
        'total_trades': 41,
        'win_rate': 53.7,
        'total_return': -0.58,
        'stop_loss_rate': 43.9,
        'risk_reward': 0.57
    }
    
    print(f"\n🎊 OPTIMIZATION RESULTS COMPARISON:")
    print(f"="*80)
    
    if results['total_trades'] > 0:
        # Calculate improvements
        new_win_rate = results['performance_metrics'].get('win_rate', 0)
        new_return = results['total_return']
        
        # Calculate stop loss rate
        stop_loss_trades = sum(1 for t in results['trades'] if t.get('exit_reason') == 'stop_loss')
        new_stop_loss_rate = stop_loss_trades / results['total_trades'] * 100
        
        # Calculate risk/reward
        winning_trades = [t for t in results['trades'] if t.get('pnl', 0) > 0]
        losing_trades = [t for t in results['trades'] if t.get('pnl', 0) < 0]
        
        if winning_trades and losing_trades:
            avg_win = sum(t.get('pnl', 0) for t in winning_trades) / len(winning_trades)
            avg_loss = sum(t.get('pnl', 0) for t in losing_trades) / len(losing_trades)
            new_risk_reward = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        else:
            new_risk_reward = 0
        
        print(f"📊 PERFORMANCE COMPARISON:")
        print(f"   Metric                 Original    Optimized    Improvement")
        print(f"   ─────────────────────  ─────────   ─────────    ───────────")
        print(f"   Total Trades           {original_results['total_trades']:8d}    {results['total_trades']:8d}    {((results['total_trades'] - original_results['total_trades']) / original_results['total_trades'] * 100):+6.1f}%")
        print(f"   Win Rate               {original_results['win_rate']:8.1f}%   {new_win_rate:8.1f}%   {(new_win_rate - original_results['win_rate']):+6.1f}%")
        print(f"   Total Return           {original_results['total_return']:8.2f}%   {new_return:8.2f}%   {(new_return - original_results['total_return']):+6.2f}%")
        print(f"   Stop Loss Rate         {original_results['stop_loss_rate']:8.1f}%   {new_stop_loss_rate:8.1f}%   {(new_stop_loss_rate - original_results['stop_loss_rate']):+6.1f}%")
        print(f"   Risk/Reward Ratio      {original_results['risk_reward']:8.2f}:1   {new_risk_reward:8.2f}:1   {((new_risk_reward - original_results['risk_reward']) / original_results['risk_reward'] * 100):+6.1f}%")
        
        # Success metrics
        improvements = 0
        if new_win_rate > original_results['win_rate']:
            improvements += 1
            print(f"   ✅ Win rate improved by {new_win_rate - original_results['win_rate']:.1f}%")
        
        if new_return > original_results['total_return']:
            improvements += 1
            print(f"   ✅ Return improved by {new_return - original_results['total_return']:.2f}%")
        
        if new_stop_loss_rate < original_results['stop_loss_rate']:
            improvements += 1
            print(f"   ✅ Stop loss rate reduced by {original_results['stop_loss_rate'] - new_stop_loss_rate:.1f}%")
        
        if new_risk_reward > original_results['risk_reward']:
            improvements += 1
            print(f"   ✅ Risk/reward improved by {((new_risk_reward - original_results['risk_reward']) / original_results['risk_reward'] * 100):.1f}%")
        
        # Overall assessment
        print(f"\n🎯 OPTIMIZATION ASSESSMENT:")
        if improvements >= 3:
            print(f"   🎉 EXCELLENT! {improvements}/4 metrics improved")
            print(f"   ✅ Optimization was highly successful")
        elif improvements >= 2:
            print(f"   👍 GOOD! {improvements}/4 metrics improved")
            print(f"   ✅ Optimization was successful")
        elif improvements >= 1:
            print(f"   ⚡ PARTIAL! {improvements}/4 metrics improved")
            print(f"   ⚠️  Some optimization success, may need fine-tuning")
        else:
            print(f"   ❌ POOR! No metrics improved")
            print(f"   🔧 Optimization needs revision")
        
        # Show pattern performance
        if results.get('pattern_performance'):
            print(f"\n🎨 OPTIMIZED PATTERN PERFORMANCE:")
            for pattern_type, stats in results['pattern_performance'].items():
                win_rate = stats.get('win_rate', 0)
                win_emoji = "🎯" if win_rate >= 60 else "⚡" if win_rate >= 50 else "⚠️"
                optimized_emoji = "✨" if "optimized" in pattern_type else ""
                print(f"   {win_emoji} {optimized_emoji} {pattern_type}: {stats['trades']} trades, "
                      f"{win_rate:.1f}% win rate, ${stats['total_pnl']:.2f} PnL")
        
        # Profitability analysis
        if new_return > 0:
            print(f"\n💰 PROFITABILITY ACHIEVED!")
            print(f"   🎉 Strategy turned profitable: {new_return:.2f}% return")
            print(f"   📈 Ready for live trading consideration")
        elif new_return > original_results['total_return']:
            print(f"\n📈 LOSS REDUCTION ACHIEVED!")
            print(f"   ✅ Loss reduced from {original_results['total_return']:.2f}% to {new_return:.2f}%")
            print(f"   🔧 Further optimization may achieve profitability")
        else:
            print(f"\n⚠️  OPTIMIZATION NEEDS WORK")
            print(f"   🔧 Consider more aggressive parameter changes")
    
    else:
        print(f"❌ CRITICAL: Optimized strategy generated no trades!")
        print(f"   🔧 Parameters may be too restrictive")
        print(f"   💡 Consider relaxing some conditions")
    
    print(f"\n🚀 NEXT STEPS:")
    if results['total_trades'] > 0 and results['total_return'] > original_results['total_return']:
        print(f"   1. ✅ Optimization successful - parameters improved")
        print(f"   2. 🧪 Test on different time periods for validation")
        print(f"   3. 📊 Test on other symbols (ETH_USDT, etc.)")
        print(f"   4. 🎯 Consider live trading with small position sizes")
        print(f"   5. 📈 Monitor performance and fine-tune as needed")
    else:
        print(f"   1. 🔧 Further parameter optimization needed")
        print(f"   2. 📊 Analyze new losing trades for additional insights")
        print(f"   3. 🎯 Consider different optimization approaches")
        print(f"   4. 📈 Test with longer time periods")
    
    print("="*80)
    print("🎊 OPTIMIZATION ANALYSIS COMPLETE!")
    print("="*80)

if __name__ == "__main__":
    main()
