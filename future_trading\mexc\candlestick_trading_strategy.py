# Candlestick Pattern-Based Trading Strategy
import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple, List
from datetime import datetime, timedelta
from candlestick_patterns import CandlestickPatterns
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG, INDICATORS, RISK_PER_TRADE, CAPITAL_USD
from logging_config import get_logger

class CandlestickTradingStrategy:
    def __init__(self):
        self.logger = get_logger(__name__)
        self.patterns = CandlestickPatterns()
        self.indicators = TechnicalIndicators()
        self.config = CANDLESTICK_STRATEGY_CONFIG
        
        # Strategy state
        self.active_positions = {}
        self.trade_history = []
        self.last_signal_time = {}
        
        self.logger.info("Candlestick Trading Strategy initialized")
    
    def validate_market_conditions(self, df_1h: pd.DataFrame, df_5m: pd.DataFrame) -> Dict[str, any]:
        """Validate overall market conditions for pattern trading"""
        try:
            if len(df_1h) < 50 or len(df_5m) < 50:
                return {'valid': False, 'reason': 'insufficient_data'}
            
            latest_1h = df_1h.iloc[-1]
            latest_5m = df_5m.iloc[-1]
            
            # Check volatility (ATR)
            atr_1h = latest_1h.get('atr', 0)
            price = latest_1h['close']
            volatility_ratio = atr_1h / price if price > 0 else 0
            
            # Market should have reasonable volatility (0.5% - 5%)
            if volatility_ratio < 0.005 or volatility_ratio > 0.05:
                return {
                    'valid': False, 
                    'reason': 'extreme_volatility',
                    'volatility_ratio': volatility_ratio
                }
            
            # Check volume conditions
            volume_avg_1h = df_1h['volume'].tail(20).mean()
            current_volume_1h = latest_1h['volume']
            volume_ratio = current_volume_1h / volume_avg_1h if volume_avg_1h > 0 else 0
            
            # Volume should be at least 50% of average
            if volume_ratio < 0.5:
                return {
                    'valid': False,
                    'reason': 'low_volume',
                    'volume_ratio': volume_ratio
                }
            
            # Check trend clarity on 1H
            ema_50 = latest_1h.get('ema_50', 0)
            ema_200 = latest_1h.get('ema_200', 0)
            
            trend_direction = 'bullish' if ema_50 > ema_200 else 'bearish'
            trend_strength = abs(ema_50 - ema_200) / ema_200 if ema_200 > 0 else 0
            
            return {
                'valid': True,
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'volatility_ratio': volatility_ratio,
                'volume_ratio': volume_ratio,
                'market_phase': self.determine_market_phase(df_1h)
            }
            
        except Exception as e:
            self.logger.error(f"Error validating market conditions: {e}")
            return {'valid': False, 'reason': 'error', 'error': str(e)}
    
    def determine_market_phase(self, df: pd.DataFrame) -> str:
        """Determine current market phase (trending, ranging, volatile)"""
        try:
            if len(df) < 20:
                return 'unknown'
            
            # Calculate price movement over last 20 periods
            recent_data = df.tail(20)
            price_range = recent_data['high'].max() - recent_data['low'].min()
            price_change = abs(recent_data['close'].iloc[-1] - recent_data['close'].iloc[0])
            
            # Calculate average true range
            atr_avg = recent_data['atr'].mean() if 'atr' in recent_data.columns else 0
            
            # Determine phase
            if price_change > price_range * 0.7:
                return 'trending'
            elif atr_avg > recent_data['close'].mean() * 0.02:
                return 'volatile'
            else:
                return 'ranging'
                
        except Exception as e:
            self.logger.error(f"Error determining market phase: {e}")
            return 'unknown'
    
    def analyze_support_resistance(self, df: pd.DataFrame, lookback: int = 50) -> Dict[str, List[float]]:
        """Identify key support and resistance levels"""
        try:
            if len(df) < lookback:
                return {'support': [], 'resistance': []}
            
            recent_data = df.tail(lookback)
            
            # Find local highs and lows
            highs = []
            lows = []
            
            for i in range(2, len(recent_data) - 2):
                current_high = recent_data.iloc[i]['high']
                current_low = recent_data.iloc[i]['low']
                
                # Local high
                if (current_high > recent_data.iloc[i-1]['high'] and 
                    current_high > recent_data.iloc[i-2]['high'] and
                    current_high > recent_data.iloc[i+1]['high'] and 
                    current_high > recent_data.iloc[i+2]['high']):
                    highs.append(current_high)
                
                # Local low
                if (current_low < recent_data.iloc[i-1]['low'] and 
                    current_low < recent_data.iloc[i-2]['low'] and
                    current_low < recent_data.iloc[i+1]['low'] and 
                    current_low < recent_data.iloc[i+2]['low']):
                    lows.append(current_low)
            
            # Cluster similar levels
            resistance_levels = self.cluster_levels(highs)
            support_levels = self.cluster_levels(lows)
            
            return {
                'support': sorted(support_levels),
                'resistance': sorted(resistance_levels, reverse=True)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing support/resistance: {e}")
            return {'support': [], 'resistance': []}
    
    def cluster_levels(self, levels: List[float], threshold: float = 0.005) -> List[float]:
        """Cluster similar price levels together"""
        if not levels:
            return []
        
        clustered = []
        sorted_levels = sorted(levels)
        
        current_cluster = [sorted_levels[0]]
        
        for level in sorted_levels[1:]:
            if abs(level - current_cluster[-1]) / current_cluster[-1] <= threshold:
                current_cluster.append(level)
            else:
                # Average the cluster and add to results
                clustered.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [level]
        
        # Add the last cluster
        if current_cluster:
            clustered.append(sum(current_cluster) / len(current_cluster))
        
        return clustered
    
    def validate_pattern_with_context(self, pattern_data: Dict, df_1h: pd.DataFrame, 
                                    df_5m: pd.DataFrame, sr_levels: Dict) -> Dict[str, any]:
        """Validate pattern with market context and support/resistance"""
        try:
            if not pattern_data['has_pattern']:
                return {'valid': False, 'reason': 'no_pattern'}
            
            strongest_pattern = pattern_data['strongest_pattern']
            current_price = df_5m.iloc[-1]['close']
            
            # Check RSI filter if enabled
            if self.config['rsi_filter_enabled']:
                current_rsi = df_5m.iloc[-1].get('rsi', 50)
                
                if strongest_pattern['signal'] == 'bullish':
                    if current_rsi > self.config['rsi_bullish_threshold']:
                        return {'valid': False, 'reason': 'rsi_filter_bullish'}
                elif strongest_pattern['signal'] == 'bearish':
                    if current_rsi < self.config['rsi_bearish_threshold']:
                        return {'valid': False, 'reason': 'rsi_filter_bearish'}
            
            # Check proximity to support/resistance
            buffer = self.config['support_resistance_buffer']
            
            if strongest_pattern['signal'] == 'bullish':
                # Should be near support for bullish patterns
                near_support = any(
                    abs(current_price - support) / support <= buffer 
                    for support in sr_levels['support']
                )
                if not near_support and sr_levels['support']:
                    return {'valid': False, 'reason': 'not_near_support'}
                    
            elif strongest_pattern['signal'] == 'bearish':
                # Should be near resistance for bearish patterns
                near_resistance = any(
                    abs(current_price - resistance) / resistance <= buffer 
                    for resistance in sr_levels['resistance']
                )
                if not near_resistance and sr_levels['resistance']:
                    return {'valid': False, 'reason': 'not_near_resistance'}
            
            # Check trend alignment
            latest_1h = df_1h.iloc[-1]
            ema_50 = latest_1h.get('ema_50', current_price)
            
            if strongest_pattern['signal'] == 'bullish':
                # For bullish patterns, prefer when price is above or near EMA50
                if current_price < ema_50 * 0.995:  # 0.5% below EMA
                    return {'valid': False, 'reason': 'trend_misalignment_bullish'}
                    
            elif strongest_pattern['signal'] == 'bearish':
                # For bearish patterns, prefer when price is below or near EMA50
                if current_price > ema_50 * 1.005:  # 0.5% above EMA
                    return {'valid': False, 'reason': 'trend_misalignment_bearish'}
            
            # Check volume confirmation
            current_volume = df_5m.iloc[-1]['volume']
            avg_volume = df_5m['volume'].tail(20).mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            if volume_ratio < self.config['volume_multiplier']:
                return {'valid': False, 'reason': 'insufficient_volume'}
            
            return {
                'valid': True,
                'pattern': strongest_pattern,
                'context_score': min(strongest_pattern['strength'] * volume_ratio, 1.0),
                'volume_ratio': volume_ratio
            }
            
        except Exception as e:
            self.logger.error(f"Error validating pattern with context: {e}")
            return {'valid': False, 'reason': 'error', 'error': str(e)}
    
    def calculate_position_sizing(self, entry_price: float, stop_loss: float, 
                                risk_amount: float = None) -> float:
        """Calculate position size based on risk management"""
        try:
            if risk_amount is None:
                risk_amount = CAPITAL_USD * RISK_PER_TRADE
            
            # Calculate risk per unit
            risk_per_unit = abs(entry_price - stop_loss)
            if risk_per_unit == 0:
                return 0
            
            # Calculate position size
            position_size = risk_amount / risk_per_unit
            
            # Apply limits
            min_position = 0.001
            max_position = CAPITAL_USD * 0.1 / entry_price  # Max 10% of capital
            
            position_size = max(min_position, min(position_size, max_position))
            
            return round(position_size, 6)
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0.001
    
    def calculate_stop_loss_take_profit(self, entry_price: float, signal: str, 
                                      atr: float, pattern_type: str) -> Dict[str, float]:
        """Calculate stop loss and take profit based on pattern type"""
        try:
            # Adjust multipliers based on pattern type
            if pattern_type in ['hammer', 'shooting_star']:
                # Reversal patterns - tighter stops, higher targets
                sl_multiplier = self.config['atr_multiplier_sl'] * 0.8
                tp1_multiplier = self.config['atr_multiplier_tp1'] * 1.2
                tp2_multiplier = self.config['atr_multiplier_tp2'] * 1.1
            elif pattern_type == 'engulfing':
                # Strong continuation patterns
                sl_multiplier = self.config['atr_multiplier_sl']
                tp1_multiplier = self.config['atr_multiplier_tp1']
                tp2_multiplier = self.config['atr_multiplier_tp2']
            else:  # doji and others
                # Conservative approach for indecision patterns
                sl_multiplier = self.config['atr_multiplier_sl'] * 1.2
                tp1_multiplier = self.config['atr_multiplier_tp1'] * 0.8
                tp2_multiplier = self.config['atr_multiplier_tp2'] * 0.9
            
            if signal == 'BUY':
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit_1 = entry_price + (atr * tp1_multiplier)
                take_profit_2 = entry_price + (atr * tp2_multiplier)
            else:  # SELL
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit_1 = entry_price - (atr * tp1_multiplier)
                take_profit_2 = entry_price - (atr * tp2_multiplier)
            
            return {
                'stop_loss': round(stop_loss, 8),
                'take_profit_1': round(take_profit_1, 8),
                'take_profit_2': round(take_profit_2, 8),
                'risk_reward_1': abs(take_profit_1 - entry_price) / abs(entry_price - stop_loss),
                'risk_reward_2': abs(take_profit_2 - entry_price) / abs(entry_price - stop_loss)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating SL/TP: {e}")
            return {
                'stop_loss': entry_price * 0.98 if signal == 'BUY' else entry_price * 1.02,
                'take_profit_1': entry_price * 1.02 if signal == 'BUY' else entry_price * 0.98,
                'take_profit_2': entry_price * 1.04 if signal == 'BUY' else entry_price * 0.96,
                'risk_reward_1': 1.0,
                'risk_reward_2': 2.0
            }

    def generate_trading_signal(self, df_1h: pd.DataFrame, df_5m: pd.DataFrame,
                              symbol: str) -> Dict[str, any]:
        """Generate complete trading signal based on candlestick patterns"""
        try:
            # Validate market conditions
            market_conditions = self.validate_market_conditions(df_1h, df_5m)
            if not market_conditions['valid']:
                return {
                    'signal': None,
                    'confidence': 0,
                    'reason': market_conditions['reason'],
                    'timestamp': datetime.now(),
                    'symbol': symbol
                }

            # Analyze support and resistance levels
            sr_levels = self.analyze_support_resistance(df_5m)

            # Detect candlestick patterns
            pattern_data = self.patterns.detect_all_patterns(df_5m)

            # Validate pattern with context
            pattern_validation = self.validate_pattern_with_context(
                pattern_data, df_1h, df_5m, sr_levels
            )

            if not pattern_validation['valid']:
                return {
                    'signal': None,
                    'confidence': 0,
                    'reason': pattern_validation['reason'],
                    'pattern_data': pattern_data,
                    'timestamp': datetime.now(),
                    'symbol': symbol
                }

            # Generate final signal
            strongest_pattern = pattern_validation['pattern']
            signal = 'BUY' if strongest_pattern['signal'] == 'bullish' else 'SELL'
            confidence = pattern_validation['context_score']

            # Calculate levels
            latest_5m = df_5m.iloc[-1]
            entry_price = latest_5m['close']
            atr = latest_5m.get('atr', entry_price * 0.02)  # Default 2% if no ATR

            levels = self.calculate_stop_loss_take_profit(
                entry_price, signal, atr, strongest_pattern['type']
            )
            levels['entry_price'] = entry_price
            levels['position_size'] = self.calculate_position_sizing(
                entry_price, levels['stop_loss']
            )

            return {
                'signal': signal,
                'confidence': confidence,
                'pattern_type': strongest_pattern['type'],
                'pattern_strength': strongest_pattern['strength'],
                'market_conditions': market_conditions,
                'support_resistance': sr_levels,
                'levels': levels,
                'volume_confirmation': pattern_validation['volume_ratio'],
                'timestamp': datetime.now(),
                'symbol': symbol
            }

        except Exception as e:
            self.logger.error(f"Error generating trading signal: {e}")
            return {
                'signal': None,
                'confidence': 0,
                'error': str(e),
                'timestamp': datetime.now(),
                'symbol': symbol
            }

    def should_exit_position(self, position_data: Dict, current_price: float,
                           current_df: pd.DataFrame) -> Dict[str, any]:
        """Determine if position should be exited based on pattern strategy"""
        try:
            entry_price = position_data['entry_price']
            stop_loss = position_data['stop_loss']
            take_profit_1 = position_data['take_profit_1']
            take_profit_2 = position_data.get('take_profit_2')
            side = position_data['side']
            pattern_type = position_data.get('pattern_type', 'unknown')

            exit_reason = None
            exit_price = current_price
            partial_exit = False

            # Standard stop loss and take profit checks
            if side == 'BUY':
                if current_price <= stop_loss:
                    exit_reason = 'stop_loss'
                elif current_price >= take_profit_1:
                    if take_profit_2 and current_price < take_profit_2:
                        exit_reason = 'take_profit_1'
                        partial_exit = True
                    else:
                        exit_reason = 'take_profit_2' if take_profit_2 else 'take_profit_1'
            else:  # SELL
                if current_price >= stop_loss:
                    exit_reason = 'stop_loss'
                elif current_price <= take_profit_1:
                    if take_profit_2 and current_price > take_profit_2:
                        exit_reason = 'take_profit_1'
                        partial_exit = True
                    else:
                        exit_reason = 'take_profit_2' if take_profit_2 else 'take_profit_1'

            # Pattern-specific exit conditions
            if not exit_reason and len(current_df) >= 3:
                # Check for reversal patterns
                current_patterns = self.patterns.detect_all_patterns(current_df)

                if current_patterns['has_pattern']:
                    current_pattern = current_patterns['strongest_pattern']

                    # Exit if opposite pattern appears with high strength
                    if ((side == 'BUY' and current_pattern['signal'] == 'bearish') or
                        (side == 'SELL' and current_pattern['signal'] == 'bullish')):
                        if current_pattern['strength'] > 0.8:
                            exit_reason = 'opposite_pattern'

            return {
                'should_exit': exit_reason is not None,
                'exit_reason': exit_reason,
                'exit_price': exit_price,
                'partial_exit': partial_exit,
                'profit_loss': self.calculate_pnl(position_data, exit_price)
            }

        except Exception as e:
            self.logger.error(f"Error checking exit conditions: {e}")
            return {'should_exit': False, 'error': str(e)}

    def calculate_pnl(self, position_data: Dict, exit_price: float) -> float:
        """Calculate profit/loss for a position"""
        try:
            entry_price = position_data['entry_price']
            quantity = position_data['quantity']
            side = position_data['side']

            if side == 'BUY':
                pnl = (exit_price - entry_price) * quantity
            else:  # SELL
                pnl = (entry_price - exit_price) * quantity

            return round(pnl, 8)

        except Exception as e:
            self.logger.error(f"Error calculating PnL: {e}")
            return 0.0

    def validate_signal(self, signal_data: Dict) -> bool:
        """Validate trading signal before execution"""
        try:
            # Check if signal exists and has minimum confidence
            if not signal_data.get('signal') or signal_data.get('confidence', 0) < 0.7:
                return False

            # Check if levels are properly calculated
            levels = signal_data.get('levels', {})
            if not levels or 'entry_price' not in levels:
                return False

            # Check risk-reward ratio (should be at least 1.5:1 for pattern trading)
            if levels.get('risk_reward_1', 0) < 1.5:
                return False

            # Check position size
            if levels.get('position_size', 0) <= 0:
                return False

            # Check pattern strength
            if signal_data.get('pattern_strength', 0) < self.config['pattern_strength_threshold']:
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating signal: {e}")
            return False

    def get_strategy_performance(self) -> Dict[str, any]:
        """Get strategy performance metrics"""
        try:
            if not self.trade_history:
                return {'total_trades': 0, 'performance': 'No trades yet'}

            total_trades = len(self.trade_history)
            winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl', 0) > 0)
            losing_trades = total_trades - winning_trades

            total_pnl = sum(trade.get('pnl', 0) for trade in self.trade_history)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Pattern-specific statistics
            pattern_stats = {}
            for trade in self.trade_history:
                pattern_type = trade.get('pattern_type', 'unknown')
                if pattern_type not in pattern_stats:
                    pattern_stats[pattern_type] = {'trades': 0, 'wins': 0, 'pnl': 0}

                pattern_stats[pattern_type]['trades'] += 1
                pattern_stats[pattern_type]['pnl'] += trade.get('pnl', 0)
                if trade.get('pnl', 0) > 0:
                    pattern_stats[pattern_type]['wins'] += 1

            avg_win = np.mean([trade['pnl'] for trade in self.trade_history if trade.get('pnl', 0) > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([trade['pnl'] for trade in self.trade_history if trade.get('pnl', 0) < 0]) if losing_trades > 0 else 0

            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate * 100, 2),
                'total_pnl': round(total_pnl, 2),
                'avg_win': round(avg_win, 2),
                'avg_loss': round(avg_loss, 2),
                'profit_factor': round(abs(avg_win * winning_trades / (avg_loss * losing_trades)), 2) if avg_loss != 0 and losing_trades > 0 else 0,
                'pattern_statistics': pattern_stats
            }

        except Exception as e:
            self.logger.error(f"Error calculating performance: {e}")
            return {'error': str(e)}
