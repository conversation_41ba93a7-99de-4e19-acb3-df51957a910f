#!/usr/bin/env python3
"""
Test script for Candlestick Pattern Trading Strategy
Validates pattern detection and strategy logic
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from logging_config import get_logger

def create_test_data():
    """Create synthetic test data with known patterns"""
    logger = get_logger(__name__)
    
    # Create base data
    dates = pd.date_range(start='2024-01-01', periods=300, freq='5T')
    np.random.seed(42)  # For reproducible results
    
    # Generate price data with some patterns
    base_price = 50000
    prices = []
    volumes = []
    
    for i in range(len(dates)):
        # Add some trend and noise
        trend = i * 0.5
        noise = np.random.normal(0, 100)
        price = base_price + trend + noise
        
        # Add specific patterns at known locations
        if i == 50:  # Hammer pattern
            open_price = price
            high_price = price + 50
            low_price = price - 200  # Long lower shadow
            close_price = price + 10
        elif i == 100:  # Shooting star pattern
            open_price = price
            high_price = price + 200  # Long upper shadow
            low_price = price - 20
            close_price = price - 10
        elif i == 150:  # Bullish engulfing (2 candles)
            if i == 149:  # Previous candle (bearish)
                open_price = price + 50
                high_price = price + 60
                low_price = price - 10
                close_price = price
            else:  # Current candle (bullish engulfing)
                open_price = price - 20
                high_price = price + 80
                low_price = price - 30
                close_price = price + 70
        else:  # Normal candle
            open_price = price
            high_price = price + abs(np.random.normal(0, 50))
            low_price = price - abs(np.random.normal(0, 50))
            close_price = price + np.random.normal(0, 30)
        
        prices.append({
            'open': open_price,
            'high': max(open_price, high_price, low_price, close_price),
            'low': min(open_price, high_price, low_price, close_price),
            'close': close_price
        })
        
        volumes.append(abs(np.random.normal(1000, 200)))
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': [p['open'] for p in prices],
        'high': [p['high'] for p in prices],
        'low': [p['low'] for p in prices],
        'close': [p['close'] for p in prices],
        'volume': volumes
    })
    
    logger.info(f"Created test data with {len(df)} candles")
    return df

def test_pattern_detection():
    """Test candlestick pattern detection"""
    logger = get_logger(__name__)
    logger.info("Testing candlestick pattern detection...")
    
    # Create test data
    df = create_test_data()
    
    # Add technical indicators
    indicators = TechnicalIndicators()
    df = indicators.add_ema(df, 50)
    df = indicators.add_ema(df, 200)
    df = indicators.add_rsi(df, 14)
    df = indicators.add_atr(df, 14)
    df = indicators.add_volume_indicators(df)
    
    # Initialize pattern detector
    patterns = CandlestickPatterns()
    
    # Test pattern detection at specific locations
    test_indices = [50, 100, 150, 200, 250]
    
    for idx in test_indices:
        if idx < len(df):
            logger.info(f"\nTesting patterns at index {idx}:")
            
            # Test individual patterns
            hammer = patterns.detect_hammer(df, idx)
            shooting_star = patterns.detect_shooting_star(df, idx)
            doji = patterns.detect_doji(df, idx)
            engulfing = patterns.detect_engulfing(df, idx)
            
            logger.info(f"  Hammer: {hammer['detected']} (strength: {hammer['strength']:.2f})")
            logger.info(f"  Shooting Star: {shooting_star['detected']} (strength: {shooting_star['strength']:.2f})")
            logger.info(f"  Doji: {doji['detected']} (strength: {doji['strength']:.2f})")
            logger.info(f"  Engulfing: {engulfing['detected']} (strength: {engulfing['strength']:.2f})")
            
            # Test combined detection
            all_patterns = patterns.detect_all_patterns(df, idx)
            if all_patterns['has_pattern']:
                strongest = all_patterns['strongest_pattern']
                logger.info(f"  Strongest: {strongest['name']} ({strongest['signal']}) - {strongest['strength']:.2f}")
    
    logger.info("Pattern detection test completed")

def test_strategy_signals():
    """Test trading strategy signal generation"""
    logger = get_logger(__name__)
    logger.info("Testing strategy signal generation...")
    
    # Create test data for both timeframes
    df_5m = create_test_data()
    
    # Create 1H data (sample every 12 candles from 5M data)
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    
    # Add technical indicators
    indicators = TechnicalIndicators()
    
    # Add indicators to both timeframes
    for df in [df_5m, df_1h]:
        df = indicators.add_ema(df, 50)
        df = indicators.add_ema(df, 200)
        df = indicators.add_rsi(df, 14)
        df = indicators.add_atr(df, 14)
        df = indicators.add_macd(df)
        df = indicators.add_volume_indicators(df)
    
    # Initialize strategy
    strategy = CandlestickTradingStrategy()
    
    # Test signal generation at different points
    test_points = [100, 150, 200, 250]
    
    for point in test_points:
        if point < len(df_5m) and point//12 < len(df_1h):
            logger.info(f"\nTesting signal generation at 5M index {point}:")
            
            # Get data slices
            df_5m_slice = df_5m.iloc[:point+1].copy()
            df_1h_slice = df_1h.iloc[:point//12+1].copy()
            
            # Generate signal
            signal_data = strategy.generate_trading_signal(
                df_1h_slice, df_5m_slice, 'BTC_USDT'
            )
            
            logger.info(f"  Signal: {signal_data.get('signal', 'None')}")
            logger.info(f"  Confidence: {signal_data.get('confidence', 0):.2f}")
            logger.info(f"  Pattern Type: {signal_data.get('pattern_type', 'None')}")
            logger.info(f"  Pattern Strength: {signal_data.get('pattern_strength', 0):.2f}")
            
            if signal_data.get('levels'):
                levels = signal_data['levels']
                logger.info(f"  Entry Price: ${levels.get('entry_price', 0):.2f}")
                logger.info(f"  Stop Loss: ${levels.get('stop_loss', 0):.2f}")
                logger.info(f"  Take Profit 1: ${levels.get('take_profit_1', 0):.2f}")
                logger.info(f"  Position Size: {levels.get('position_size', 0):.6f}")
                logger.info(f"  Risk/Reward 1: {levels.get('risk_reward_1', 0):.2f}")
    
    logger.info("Strategy signal test completed")

def test_market_conditions():
    """Test market condition validation"""
    logger = get_logger(__name__)
    logger.info("Testing market condition validation...")
    
    # Create test data
    df_5m = create_test_data()
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    
    # Add indicators
    indicators = TechnicalIndicators()
    for df in [df_5m, df_1h]:
        df = indicators.add_ema(df, 50)
        df = indicators.add_ema(df, 200)
        df = indicators.add_rsi(df, 14)
        df = indicators.add_atr(df, 14)
        df = indicators.add_volume_indicators(df)
    
    # Initialize strategy
    strategy = CandlestickTradingStrategy()
    
    # Test market condition validation
    conditions = strategy.validate_market_conditions(df_1h, df_5m)
    
    logger.info(f"Market conditions valid: {conditions.get('valid', False)}")
    if conditions.get('valid'):
        logger.info(f"  Trend direction: {conditions.get('trend_direction', 'unknown')}")
        logger.info(f"  Trend strength: {conditions.get('trend_strength', 0):.3f}")
        logger.info(f"  Volatility ratio: {conditions.get('volatility_ratio', 0):.3f}")
        logger.info(f"  Volume ratio: {conditions.get('volume_ratio', 0):.2f}")
        logger.info(f"  Market phase: {conditions.get('market_phase', 'unknown')}")
    else:
        logger.info(f"  Reason: {conditions.get('reason', 'unknown')}")
    
    # Test support/resistance analysis
    sr_levels = strategy.analyze_support_resistance(df_5m)
    logger.info(f"\nSupport levels found: {len(sr_levels['support'])}")
    logger.info(f"Resistance levels found: {len(sr_levels['resistance'])}")
    
    if sr_levels['support']:
        logger.info(f"  Support levels: {[f'${level:.2f}' for level in sr_levels['support'][:3]]}")
    if sr_levels['resistance']:
        logger.info(f"  Resistance levels: {[f'${level:.2f}' for level in sr_levels['resistance'][:3]]}")
    
    logger.info("Market condition test completed")

def run_all_tests():
    """Run all tests"""
    logger = get_logger(__name__)
    
    logger.info("="*80)
    logger.info("CANDLESTICK PATTERN STRATEGY TESTING")
    logger.info("="*80)
    
    try:
        test_pattern_detection()
        test_strategy_signals()
        test_market_conditions()
        
        logger.info("\n" + "="*80)
        logger.info("ALL TESTS COMPLETED SUCCESSFULLY")
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
