#!/usr/bin/env python3
"""
Test the relaxed parameters to verify they will generate trades
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG

def test_relaxed_parameters():
    """Test if the new relaxed parameters will generate trades"""
    print("="*80)
    print("🧪 TESTING RELAXED PARAMETERS")
    print("="*80)
    
    # Show new configuration
    print("📊 NEW RELAXED CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"   {key}: {value}")
    
    print(f"\n🔬 TESTING PATTERN DETECTION WITH RELAXED PARAMETERS:")
    
    # Create realistic test data with embedded patterns
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    patterns_embedded = 0
    
    for i in range(len(dates)):
        price = base_price + i * 1 + np.random.normal(0, 30)
        
        # Embed patterns more frequently for testing
        if i % 50 == 10:  # Hammer every 50 candles
            open_price = price
            high_price = price + 25
            low_price = price - 100  # Long lower shadow
            close_price = price + 15
            volume = 1500
            patterns_embedded += 1
            
        elif i % 50 == 25:  # Shooting star
            open_price = price
            high_price = price + 100  # Long upper shadow
            low_price = price - 25
            close_price = price - 15
            volume = 1400
            patterns_embedded += 1
            
        elif i % 50 == 40:  # Doji
            open_price = price
            high_price = price + 50
            low_price = price - 50
            close_price = price + 3  # Small body
            volume = 1200
            patterns_embedded += 1
            
        else:  # Normal candle
            open_price = price
            high_price = price + abs(np.random.normal(0, 25))
            low_price = price - abs(np.random.normal(0, 25))
            close_price = price + np.random.normal(0, 15)
            volume = abs(np.random.normal(1000, 200))
        
        # Ensure OHLC logic
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': max(0.01, open_price),
            'high': max(0.01, high_price),
            'low': max(0.01, low_price),
            'close': max(0.01, close_price),
            'volume': max(1, volume)
        })
    
    df = pd.DataFrame(data)
    print(f"   Created {len(df)} candles with {patterns_embedded} embedded patterns")
    
    # Add technical indicators
    indicators = TechnicalIndicators()
    df['ema_50'] = indicators.ema(df['close'], 50)
    df['ema_200'] = indicators.ema(df['close'], 200)
    df['rsi'] = indicators.rsi(df['close'], 14)
    df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
    
    macd_data = indicators.macd(df['close'])
    df['macd'] = macd_data['macd']
    df['macd_signal'] = macd_data['signal']
    df['macd_histogram'] = macd_data['histogram']
    
    df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
    df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    # Test pattern detection
    patterns = CandlestickPatterns()
    detected_patterns = 0
    strong_patterns = 0
    
    print(f"\n🔍 SCANNING FOR PATTERNS:")
    
    for i in range(200, len(df)):  # Start after enough data for indicators
        all_patterns = patterns.detect_all_patterns(df, i)
        if all_patterns['has_pattern']:
            detected_patterns += 1
            strongest = all_patterns['strongest_pattern']
            if strongest['strength'] >= CANDLESTICK_STRATEGY_CONFIG['pattern_strength_threshold']:
                strong_patterns += 1
                if strong_patterns <= 5:  # Show first 5 strong patterns
                    print(f"   Pattern {strong_patterns}: {strongest['name']} at index {i} "
                          f"(strength: {strongest['strength']:.3f}, signal: {strongest['signal']})")
    
    detection_rate = detected_patterns / (len(df) - 200) * 100
    strong_rate = strong_patterns / (len(df) - 200) * 100
    
    print(f"\n📊 PATTERN DETECTION RESULTS:")
    print(f"   Total patterns detected: {detected_patterns}")
    print(f"   Strong patterns (>= {CANDLESTICK_STRATEGY_CONFIG['pattern_strength_threshold']}): {strong_patterns}")
    print(f"   Detection rate: {detection_rate:.2f}%")
    print(f"   Strong pattern rate: {strong_rate:.2f}%")
    
    # Test strategy signal generation
    print(f"\n🎯 TESTING STRATEGY SIGNAL GENERATION:")
    
    # Create 1H data for strategy testing
    df_1h = df.iloc[::12].copy().reset_index(drop=True)
    df_1h = df_1h.copy()
    
    # Add indicators to 1H data
    df_1h['ema_50'] = indicators.ema(df_1h['close'], 50)
    df_1h['ema_200'] = indicators.ema(df_1h['close'], 200)
    df_1h['rsi'] = indicators.rsi(df_1h['close'], 14)
    df_1h['atr'] = indicators.atr(df_1h['high'], df_1h['low'], df_1h['close'], 14)
    
    macd_1h = indicators.macd(df_1h['close'])
    df_1h['macd'] = macd_1h['macd']
    df_1h['macd_signal'] = macd_1h['signal']
    df_1h['macd_histogram'] = macd_1h['histogram']
    
    df_1h['volume_sma'] = indicators.volume_sma(df_1h['volume'], 20)
    df_1h['volume_ratio'] = indicators.volume_ratio(df_1h['volume'], 20)
    
    strategy = CandlestickTradingStrategy()
    signals_generated = 0
    
    # Test signal generation at various points
    test_points = range(250, len(df), 50)  # Test every 50 candles
    
    for point in test_points:
        if point < len(df):
            df_5m_slice = df.iloc[:point+1].copy()
            h1_index = min(point // 12, len(df_1h) - 1)
            df_1h_slice = df_1h.iloc[:h1_index+1].copy()
            
            if len(df_1h_slice) >= 50:  # Need enough 1H data
                signal_data = strategy.generate_trading_signal(df_1h_slice, df_5m_slice, 'BTC_USDT')
                
                if signal_data.get('signal'):
                    signals_generated += 1
                    if signals_generated <= 3:  # Show first 3 signals
                        print(f"   Signal {signals_generated}: {signal_data['signal']} "
                              f"{signal_data.get('pattern_type', 'unknown')} at index {point} "
                              f"(confidence: {signal_data['confidence']:.3f})")
    
    signal_rate = signals_generated / len(list(test_points)) * 100
    
    print(f"\n📈 SIGNAL GENERATION RESULTS:")
    print(f"   Total signals generated: {signals_generated}")
    print(f"   Signal generation rate: {signal_rate:.2f}%")
    print(f"   Test points evaluated: {len(list(test_points))}")
    
    # Provide recommendations
    print(f"\n💡 ANALYSIS RESULTS:")
    if strong_patterns > 0:
        print(f"   ✅ SUCCESS! Relaxed parameters detect {strong_patterns} strong patterns")
        print(f"   ✅ Pattern detection rate improved to {strong_rate:.2f}%")
    else:
        print(f"   ⚠️  Still no strong patterns detected")
        print(f"   💡 Consider lowering pattern_strength_threshold to 0.15 or 0.1")
    
    if signals_generated > 0:
        print(f"   ✅ SUCCESS! Strategy generates {signals_generated} trading signals")
        print(f"   ✅ Signal rate: {signal_rate:.2f}%")
    else:
        print(f"   ⚠️  No trading signals generated")
        print(f"   💡 Market condition filters may still be too strict")
    
    print(f"\n🚀 RECOMMENDATIONS:")
    if strong_patterns > 0 and signals_generated > 0:
        print(f"   ✅ Parameters look good! Ready for backtesting")
        print(f"   📊 Expected trades in 7-month period: {signals_generated * 7 * 30 // 7} trades")
    elif strong_patterns > 0:
        print(f"   ⚖️  Patterns detected but no signals - check market condition filters")
        print(f"   💡 Consider disabling more filters or relaxing thresholds")
    else:
        print(f"   📉 Need even more relaxed parameters")
        print(f"   💡 Try pattern_strength_threshold = 0.15")

def main():
    """Main test function"""
    test_relaxed_parameters()
    
    print(f"\n" + "="*80)
    print(f"🎯 NEXT STEPS:")
    print(f"="*80)
    print(f"1. If test shows good results, run:")
    print(f"   python run_candlestick_backtest.py --symbol BTC_USDT --start-date 2024-11-01 --end-date 2024-12-31 --verbose")
    print(f"")
    print(f"2. If still no trades, further relax parameters:")
    print(f"   - Lower pattern_strength_threshold to 0.15 or 0.1")
    print(f"   - Increase support_resistance_buffer to 0.03")
    print(f"   - Check market condition validation logic")
    print(f"")
    print(f"3. Monitor with --verbose flag to see real-time pattern detection")

if __name__ == "__main__":
    main()
