#!/usr/bin/env python3
"""
Test the enhanced backtest with mock data to show the logging features
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time

from run_candlestick_backtest import Enhanced<PERSON>andlestickBacktestEngine
from config import CANDLESTICK_STRATEGY_CONFIG

class MockEnhancedBacktestEngine(EnhancedCandlestickBacktestEngine):
    """Mock version for testing with synthetic data"""
    
    def fetch_historical_data(self, symbol: str, interval: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Override to return mock data"""
        self.print_progress(f"📥 Generating mock {interval} data for {symbol}...")
        
        # Simulate API delay
        time.sleep(0.5)
        
        # Create realistic mock data
        if interval == 'Hour1':
            freq = '1H'
            base_volatility = 200
            pattern_frequency = 50  # Every 50 candles
        else:  # Min5
            freq = '5min'
            base_volatility = 50
            pattern_frequency = 200  # Every 200 candles
        
        dates = pd.date_range(start=start_time, end=end_time, freq=freq)
        
        # Limit to reasonable size for demo
        if len(dates) > 2000:
            dates = dates[:2000]
        
        np.random.seed(42)  # Consistent data
        base_price = 50000
        current_price = base_price
        
        data = []
        patterns_added = 0
        
        for i, date in enumerate(dates):
            # Price movement with trend
            trend = i * 0.5
            noise = np.random.normal(0, base_volatility)
            current_price = max(1000, current_price + trend + noise * 0.1)
            
            # Create OHLC
            open_price = current_price
            volatility = abs(np.random.normal(0, base_volatility/2))
            
            # Add patterns at specific intervals
            if i % pattern_frequency == 50 and i > 50:  # Hammer
                high_price = open_price + volatility * 0.3
                low_price = open_price - volatility * 2.5  # Long lower shadow
                close_price = open_price + volatility * 0.2
                volume = abs(np.random.normal(1500, 300))
                patterns_added += 1
                
            elif i % pattern_frequency == 100 and i > 50:  # Shooting star
                high_price = open_price + volatility * 2.5  # Long upper shadow
                low_price = open_price - volatility * 0.3
                close_price = open_price - volatility * 0.2
                volume = abs(np.random.normal(1400, 300))
                patterns_added += 1
                
            elif i % pattern_frequency == 150 and i > 50:  # Doji
                high_price = open_price + volatility * 0.8
                low_price = open_price - volatility * 0.8
                close_price = open_price + volatility * 0.05  # Small body
                volume = abs(np.random.normal(1200, 300))
                patterns_added += 1
                
            else:  # Normal candle
                high_price = open_price + abs(np.random.normal(0, volatility))
                low_price = open_price - abs(np.random.normal(0, volatility))
                close_price = open_price + np.random.normal(0, volatility/3)
                volume = abs(np.random.normal(1000, 200))
            
            # Ensure OHLC logic
            high_price = max(open_price, high_price, low_price, close_price)
            low_price = min(open_price, high_price, low_price, close_price)
            
            data.append({
                'timestamp': date,
                'open': max(0.01, open_price),
                'high': max(0.01, high_price),
                'low': max(0.01, low_price),
                'close': max(0.01, close_price),
                'volume': max(1, volume)
            })
            
            current_price = close_price
        
        df = pd.DataFrame(data)
        self.print_progress(f"✅ Generated {len(df)} {interval} candles with {patterns_added} embedded patterns")
        
        return df

def main():
    """Test the enhanced backtest engine"""
    print("="*80)
    print("🧪 TESTING ENHANCED CANDLESTICK BACKTEST ENGINE")
    print("="*80)
    print("This demo shows the enhanced logging and progress display")
    print("using synthetic data with embedded candlestick patterns.")
    print("="*80)
    
    # Show current configuration
    print("\n⚙️  CURRENT STRATEGY CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"   {key}: {value}")
    
    print("\n🚀 Starting enhanced backtest demo...")
    
    # Initialize enhanced engine with verbose logging
    engine = MockEnhancedBacktestEngine(verbose=True)
    
    # Run backtest with shorter period for demo
    symbol = 'BTC_USDT'
    start_date = '2024-06-01'
    end_date = '2024-06-15'  # 2 weeks for demo
    
    print(f"\n📊 Running backtest for {symbol} from {start_date} to {end_date}")
    print("🔍 Watch for real-time progress updates, trade notifications, and performance tracking!")
    print("-" * 80)
    
    # Run the backtest
    results = engine.run_backtest(symbol, start_date, end_date)
    
    if 'error' in results:
        print(f"\n❌ Backtest failed: {results['error']}")
        return
    
    # The enhanced engine already shows detailed results
    # But let's add a final summary
    print(f"\n🎊 DEMO COMPLETED SUCCESSFULLY!")
    print(f"📁 Results saved to JSON file")
    print(f"⏱️  Total time: {results.get('backtest_duration_seconds', 0):.1f} seconds")
    
    if results['total_trades'] > 0:
        print(f"\n📈 QUICK SUMMARY:")
        print(f"   💰 Return: {results['total_return']:.2f}%")
        print(f"   🎯 Win Rate: {results['performance_metrics'].get('win_rate', 0):.1f}%")
        print(f"   📊 Profit Factor: {results['performance_metrics'].get('profit_factor', 0):.2f}")
        print(f"   🔢 Total Trades: {results['total_trades']}")
    else:
        print(f"\n⚠️  No trades were generated in this demo.")
        print(f"   This could be due to:")
        print(f"   • Strict pattern detection thresholds")
        print(f"   • Market condition filters")
        print(f"   • Short demo period")
        print(f"   Try adjusting the configuration or running a longer backtest.")
    
    print(f"\n✨ The enhanced backtest engine provides:")
    print(f"   🔄 Real-time progress updates")
    print(f"   📊 Live performance tracking")
    print(f"   🎯 Trade-by-trade notifications")
    print(f"   📈 Pattern performance analysis")
    print(f"   💾 Detailed result logging")
    
    print("\n" + "="*80)
    print("🎉 ENHANCED BACKTEST DEMO COMPLETED!")
    print("="*80)

if __name__ == "__main__":
    main()
