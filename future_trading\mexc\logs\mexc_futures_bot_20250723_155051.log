2025-07-23 15:50:51 - root - INFO - setup_logging:68 - ================================================================================
2025-07-23 15:50:51 - root - INFO - setup_logging:69 - MEXC FUTURES TRADING BOT - NEW SESSION STARTED
2025-07-23 15:50:51 - root - INFO - setup_logging:70 - Log file: logs\mexc_futures_bot_20250723_155051.log
2025-07-23 15:50:51 - root - INFO - setup_logging:71 - Log level: INFO
2025-07-23 15:50:51 - root - INFO - setup_logging:72 - ================================================================================
2025-07-23 15:50:51 - mexc_api - INFO - __init__:21 - Initializing MEXC API client
2025-07-23 15:50:51 - mexc_api - INFO - __init__:22 - Base URL: https://contract.mexc.com
2025-07-23 15:50:51 - mexc_api - INFO - __init__:23 - API Key: mx0vglqq1w...2VIp
2025-07-23 15:50:51 - mexc_api - INFO - __init__:31 - MEXC API client initialized successfully
2025-07-23 15:50:51 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: True)
2025-07-23 15:50:51 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: True)
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:23 - SentimentAnalyzer initialized
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:24 - Gemini API configured: Yes
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:25 - OpenAI API configured: Yes
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:26 - AI Config: {'primary_provider': 'gemini', 'fallback_provider': 'openai', 'use_both': True, 'consensus_threshold': 0.7}
2025-07-23 15:50:51 - trading_strategy - INFO - __init__:20 - TradingStrategy initialized
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:23 - SentimentAnalyzer initialized
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:24 - Gemini API configured: Yes
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:25 - OpenAI API configured: Yes
2025-07-23 15:50:51 - sentiment_analyzer - INFO - __init__:26 - AI Config: {'primary_provider': 'gemini', 'fallback_provider': 'openai', 'use_both': True, 'consensus_threshold': 0.7}
2025-07-23 15:50:51 - backtest_engine - INFO - __init__:35 - MEXC Backtest Engine initialized
2025-07-23 15:50:51 - backtest_engine - INFO - run_backtest:327 - Starting backtest for BTC_USDT: 2025-07-16 to 2025-07-23
2025-07-23 15:50:51 - backtest_engine - INFO - fetch_historical_data:40 - Fetching historical data for BTC_USDT from 2025-07-16 00:00:00 to 2025-07-23 00:00:00
2025-07-23 15:50:51 - backtest_engine - INFO - fetch_historical_data:69 - Fetching chunk: 2025-07-16 00:00:00 to 2025-07-23 00:00:00
2025-07-23 15:50:52 - backtest_engine - INFO - fetch_historical_data:100 - Fetched 169 candles for BTC_USDT
2025-07-23 15:50:52 - backtest_engine - INFO - fetch_historical_data:40 - Fetching historical data for BTC_USDT from 2025-07-16 00:00:00 to 2025-07-23 00:00:00
2025-07-23 15:50:52 - backtest_engine - INFO - fetch_historical_data:69 - Fetching chunk: 2025-07-16 00:00:00 to 2025-07-23 00:00:00
2025-07-23 15:50:52 - backtest_engine - INFO - fetch_historical_data:100 - Fetched 2001 candles for BTC_USDT
2025-07-23 15:50:52 - backtest_engine - INFO - run_backtest:351 - Backtesting with 169 trend candles and 2001 entry candles
2025-07-23 15:50:53 - backtest_engine - INFO - run_backtest:395 - Backtest completed successfully
2025-07-23 15:50:53 - backtest_engine - INFO - save_results_to_file:523 - Backtest results saved to mexc_backtest_BTC_USDT_20250723_155053.json
