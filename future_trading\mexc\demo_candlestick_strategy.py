#!/usr/bin/env python3
"""
Demo script for Candlestick Pattern Trading Strategy
Shows how the strategy works with sample data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG

def create_sample_data_with_patterns():
    """Create sample data with known candlestick patterns"""
    print("Creating sample data with candlestick patterns...")
    
    # Create 200 candles of sample data
    dates = pd.date_range(start='2024-01-01', periods=200, freq='5T')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        # Base price with trend and noise
        trend = i * 2  # Slight uptrend
        noise = np.random.normal(0, 50)
        price = base_price + trend + noise
        
        # Create specific patterns at known locations
        if i == 50:  # Hammer pattern
            open_price = price
            high_price = price + 30
            low_price = price - 150  # Long lower shadow
            close_price = price + 20
            volume = 1500  # Higher volume
            print(f"  Added HAMMER pattern at index {i}")
            
        elif i == 100:  # Shooting star pattern
            open_price = price
            high_price = price + 150  # Long upper shadow
            low_price = price - 20
            close_price = price - 15
            volume = 1400  # Higher volume
            print(f"  Added SHOOTING STAR pattern at index {i}")
            
        elif i == 149:  # Setup for bullish engulfing (bearish candle)
            open_price = price + 40
            high_price = price + 50
            low_price = price - 10
            close_price = price
            volume = 1000
            
        elif i == 150:  # Bullish engulfing pattern
            open_price = price - 20
            high_price = price + 60
            low_price = price - 30
            close_price = price + 50  # Engulfs previous candle
            volume = 1600  # Higher volume
            print(f"  Added BULLISH ENGULFING pattern at index {i}")
            
        else:  # Normal candle
            volatility = 30
            open_price = price
            high_price = price + abs(np.random.normal(0, volatility))
            low_price = price - abs(np.random.normal(0, volatility))
            close_price = price + np.random.normal(0, volatility/2)
            volume = abs(np.random.normal(1000, 200))
        
        data.append({
            'timestamp': dates[i],
            'open': max(0.01, open_price),
            'high': max(open_price, high_price, low_price, close_price),
            'low': min(open_price, high_price, low_price, close_price),
            'close': max(0.01, close_price),
            'volume': max(1, volume)
        })
    
    df = pd.DataFrame(data)
    print(f"Created {len(df)} candles with patterns at indices: 50 (Hammer), 100 (Shooting Star), 150 (Bullish Engulfing)")
    return df

def add_technical_indicators(df):
    """Add technical indicators to the dataframe"""
    print("Adding technical indicators...")

    indicators = TechnicalIndicators()

    # Add EMAs
    df['ema_50'] = indicators.ema(df['close'], 50)
    df['ema_200'] = indicators.ema(df['close'], 200)

    # Add RSI
    df['rsi'] = indicators.rsi(df['close'], 14)

    # Add ATR
    df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)

    # Add MACD
    macd_data = indicators.macd(df['close'])
    df['macd'] = macd_data['macd']
    df['macd_signal'] = macd_data['signal']
    df['macd_histogram'] = macd_data['histogram']

    # Add volume indicators
    df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
    df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)

    print("Technical indicators added successfully")
    return df

def demonstrate_pattern_detection():
    """Demonstrate pattern detection capabilities"""
    print("\n" + "="*60)
    print("PATTERN DETECTION DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    df = create_sample_data_with_patterns()
    df = add_technical_indicators(df)
    
    # Initialize pattern detector
    patterns = CandlestickPatterns()
    
    # Test pattern detection at known pattern locations
    test_indices = [50, 100, 150]  # Where we placed patterns
    
    for idx in test_indices:
        print(f"\nAnalyzing patterns at index {idx}:")
        print(f"  Price: O={df.iloc[idx]['open']:.2f}, H={df.iloc[idx]['high']:.2f}, "
              f"L={df.iloc[idx]['low']:.2f}, C={df.iloc[idx]['close']:.2f}")
        print(f"  Volume: {df.iloc[idx]['volume']:.0f}")
        
        # Detect all patterns
        all_patterns = patterns.detect_all_patterns(df, idx)
        
        if all_patterns['has_pattern']:
            strongest = all_patterns['strongest_pattern']
            print(f"  ✓ PATTERN DETECTED: {strongest['name'].upper()}")
            print(f"    Signal: {strongest['signal']}")
            print(f"    Strength: {strongest['strength']:.3f}")
            print(f"    Type: {strongest['type']}")
        else:
            print(f"  ✗ No significant patterns detected")
        
        # Show individual pattern results
        for pattern_name, pattern_data in all_patterns['patterns'].items():
            if pattern_data['detected']:
                print(f"    - {pattern_name}: {pattern_data['strength']:.3f}")

def demonstrate_strategy_signals():
    """Demonstrate trading strategy signal generation"""
    print("\n" + "="*60)
    print("TRADING STRATEGY DEMONSTRATION")
    print("="*60)
    
    # Create data for both timeframes
    df_5m = create_sample_data_with_patterns()
    df_5m = add_technical_indicators(df_5m)
    
    # Create 1H data by sampling every 12 candles
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    df_1h = add_technical_indicators(df_1h)
    
    # Initialize strategy
    strategy = CandlestickTradingStrategy()
    
    print(f"\nStrategy Configuration:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"  {key}: {value}")
    
    # Test signal generation at pattern locations
    test_points = [50, 100, 150]
    
    for point in test_points:
        if point < len(df_5m):
            print(f"\n--- Testing Signal Generation at Index {point} ---")
            
            # Get data slices up to this point
            df_5m_slice = df_5m.iloc[:point+1].copy()
            df_1h_slice = df_1h.iloc[:point//12+1].copy()
            
            # Generate trading signal
            signal_data = strategy.generate_trading_signal(
                df_1h_slice, df_5m_slice, 'BTC_USDT'
            )
            
            print(f"Signal: {signal_data.get('signal', 'NONE')}")
            print(f"Confidence: {signal_data.get('confidence', 0):.3f}")
            
            if signal_data.get('signal'):
                print(f"Pattern Type: {signal_data.get('pattern_type', 'unknown')}")
                print(f"Pattern Strength: {signal_data.get('pattern_strength', 0):.3f}")
                
                levels = signal_data.get('levels', {})
                if levels:
                    print(f"Entry Price: ${levels.get('entry_price', 0):.2f}")
                    print(f"Stop Loss: ${levels.get('stop_loss', 0):.2f}")
                    print(f"Take Profit 1: ${levels.get('take_profit_1', 0):.2f}")
                    print(f"Take Profit 2: ${levels.get('take_profit_2', 0):.2f}")
                    print(f"Position Size: {levels.get('position_size', 0):.6f}")
                    print(f"Risk/Reward 1: {levels.get('risk_reward_1', 0):.2f}:1")
                    print(f"Risk/Reward 2: {levels.get('risk_reward_2', 0):.2f}:1")
                
                # Market conditions
                market_conditions = signal_data.get('market_conditions', {})
                if market_conditions.get('valid'):
                    print(f"Market Trend: {market_conditions.get('trend_direction', 'unknown')}")
                    print(f"Trend Strength: {market_conditions.get('trend_strength', 0):.3f}")
                    print(f"Market Phase: {market_conditions.get('market_phase', 'unknown')}")
            else:
                reason = signal_data.get('reason', 'unknown')
                print(f"No signal generated. Reason: {reason}")

def demonstrate_risk_management():
    """Demonstrate risk management features"""
    print("\n" + "="*60)
    print("RISK MANAGEMENT DEMONSTRATION")
    print("="*60)
    
    # Sample trade parameters
    entry_price = 50000
    atr = 500  # $500 ATR
    
    strategy = CandlestickTradingStrategy()
    
    # Test different pattern types
    pattern_types = ['hammer', 'shooting_star', 'engulfing', 'doji']
    
    for pattern_type in pattern_types:
        print(f"\n--- Risk Management for {pattern_type.upper()} Pattern ---")
        
        # Calculate levels for BUY signal
        levels_buy = strategy.calculate_stop_loss_take_profit(
            entry_price, 'BUY', atr, pattern_type
        )
        
        print(f"BUY Signal:")
        print(f"  Entry: ${entry_price:.2f}")
        print(f"  Stop Loss: ${levels_buy['stop_loss']:.2f} "
              f"({abs(entry_price - levels_buy['stop_loss']):.2f} risk)")
        print(f"  Take Profit 1: ${levels_buy['take_profit_1']:.2f} "
              f"({levels_buy['risk_reward_1']:.2f}:1 RR)")
        print(f"  Take Profit 2: ${levels_buy['take_profit_2']:.2f} "
              f"({levels_buy['risk_reward_2']:.2f}:1 RR)")
        
        # Calculate position size
        position_size = strategy.calculate_position_sizing(
            entry_price, levels_buy['stop_loss']
        )
        print(f"  Position Size: {position_size:.6f} BTC")
        print(f"  Risk Amount: ${abs(entry_price - levels_buy['stop_loss']) * position_size:.2f}")

def main():
    """Main demonstration function"""
    print("="*80)
    print("CANDLESTICK PATTERN TRADING STRATEGY DEMONSTRATION")
    print("="*80)
    print("This demo shows how the candlestick pattern trading strategy works")
    print("with sample data containing known patterns.")
    print("="*80)
    
    try:
        # Run demonstrations
        demonstrate_pattern_detection()
        demonstrate_strategy_signals()
        demonstrate_risk_management()
        
        print("\n" + "="*80)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("\nNext steps:")
        print("1. Run 'python run_candlestick_backtest.py' for full backtesting")
        print("2. Use '--optimize' flag for parameter optimization")
        print("3. Check CANDLESTICK_STRATEGY_README.md for detailed documentation")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
