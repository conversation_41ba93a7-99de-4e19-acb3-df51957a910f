#!/usr/bin/env python3
"""
Test backtest with forced trades to verify infrastructure works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_candlestick_backtest import EnhancedCandlestickBacktestEngine
from forced_trade_strategy import ForcedTradeStrategy
from config import CANDLESTICK_STRATEGY_CONFIG

class ForcedTradeBacktestEngine(EnhancedCandlestickBacktestEngine):
    """Backtest engine that uses forced trade strategy"""
    
    def __init__(self, verbose=False):
        super().__init__(verbose)
        # Replace the strategy with forced trade strategy
        self.strategy = ForcedTradeStrategy()
        self.print_progress("🎯 Using FORCED TRADE strategy - trades guaranteed!", force=True)

def main():
    """Test with forced trades"""
    print("="*80)
    print("🎯 TESTING BACKTEST WITH FORCED TRADES")
    print("="*80)
    print("This test will force trades every 100 candles to verify")
    print("that the backtesting infrastructure works correctly.")
    print("="*80)
    
    # Show configuration
    print("\n⚙️ CURRENT CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"   {key}: {value}")
    
    print(f"\n🚀 FORCED TRADE SETTINGS:")
    print(f"   • Trade interval: Every 100 candles")
    print(f"   • Pattern type: 'forced_pattern'")
    print(f"   • Alternates: BUY, SELL, BUY, SELL...")
    print(f"   • Confidence: 0.8 (high)")
    
    # Initialize forced trade engine
    engine = ForcedTradeBacktestEngine(verbose=True)
    
    # Run backtest on recent period
    symbol = 'BTC_USDT'
    start_date = '2024-12-01'
    end_date = '2024-12-31'
    
    print(f"\n📊 Running FORCED TRADE backtest:")
    print(f"   Symbol: {symbol}")
    print(f"   Period: {start_date} to {end_date}")
    print(f"   Expected trades: ~85 trades (8580 candles / 100)")
    print("-" * 80)
    
    # Run the backtest
    results = engine.run_backtest(symbol, start_date, end_date)
    
    if 'error' in results:
        print(f"\n❌ Forced trade test failed: {results['error']}")
        return
    
    # Analyze results
    print(f"\n🎊 FORCED TRADE TEST RESULTS:")
    print(f"="*60)
    
    if results['total_trades'] > 0:
        print(f"✅ SUCCESS! Generated {results['total_trades']} forced trades")
        print(f"💰 Final capital: ${results['final_capital']:.2f}")
        print(f"📈 Total return: {results['total_return']:.2f}%")
        
        if 'performance_metrics' in results:
            metrics = results['performance_metrics']
            print(f"🎯 Win rate: {metrics.get('win_rate', 0):.1f}%")
            print(f"📊 Profit factor: {metrics.get('profit_factor', 0):.2f}")
            print(f"💵 Average win: ${metrics.get('avg_win', 0):.2f}")
            print(f"💸 Average loss: ${metrics.get('avg_loss', 0):.2f}")
        
        # Show some sample trades
        if results.get('trades'):
            print(f"\n📋 SAMPLE FORCED TRADES (first 5):")
            for i, trade in enumerate(results['trades'][:5], 1):
                pnl = trade.get('pnl', 0)
                pnl_emoji = "✅" if pnl > 0 else "❌" if pnl < 0 else "➡️"
                print(f"   {i}. {pnl_emoji} {trade.get('side', 'N/A')} forced_pattern - "
                      f"Entry: ${trade.get('entry_price', 0):.2f} - "
                      f"Exit: {trade.get('exit_reason', 'N/A')} - "
                      f"PnL: ${pnl:.2f}")
        
        print(f"\n🎉 CONCLUSION: Backtesting infrastructure works perfectly!")
        print(f"   The issue is in the candlestick pattern detection logic,")
        print(f"   not in the backtesting engine itself.")
        
    else:
        print(f"❌ CRITICAL ERROR: Even forced trades failed!")
        print(f"   This indicates a fundamental issue in the backtesting engine.")
        print(f"   Check the execute_trade() method and position management.")
    
    print(f"\n🔍 NEXT STEPS:")
    if results['total_trades'] > 0:
        print(f"   1. ✅ Backtesting infrastructure confirmed working")
        print(f"   2. 🔧 Debug why real patterns aren't being detected")
        print(f"   3. 📊 Check pattern detection logic in real market data")
        print(f"   4. 🎯 Consider using simpler pattern definitions")
    else:
        print(f"   1. ❌ Fix fundamental backtesting engine issues")
        print(f"   2. 🔧 Check execute_trade() and position management")
        print(f"   3. 📊 Verify data processing pipeline")
    
    print("="*80)

if __name__ == "__main__":
    main()
