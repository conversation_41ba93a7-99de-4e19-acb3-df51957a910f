#!/usr/bin/env python3
"""
Quick test backtest for Candlestick Pattern Trading Strategy
Uses a shorter date range for faster testing
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from candlestick_backtest_engine import CandlestickBacktestEngine
from config import TRADING_PAIRS, CANDLESTICK_STRATEGY_CONFIG
from logging_config import get_logger

def create_mock_historical_data(symbol: str, start_date: str, end_date: str):
    """Create mock historical data for testing"""
    logger = get_logger(__name__)
    logger.info(f"Creating mock data for {symbol} from {start_date} to {end_date}")
    
    start_time = datetime.strptime(start_date, '%Y-%m-%d')
    end_time = datetime.strptime(end_date, '%Y-%m-%d')
    
    # Create 5-minute intervals
    dates_5m = pd.date_range(start=start_time, end=end_time, freq='5min')
    
    # Create 1-hour intervals  
    dates_1h = pd.date_range(start=start_time, end=end_time, freq='1H')
    
    np.random.seed(42)  # For reproducible results
    base_price = 50000
    
    def create_candle_data(dates, base_volatility=100):
        data = []
        current_price = base_price
        
        for i, date in enumerate(dates):
            # Add trend and noise
            trend = i * 0.1  # Slight uptrend
            noise = np.random.normal(0, base_volatility)
            current_price = max(1000, current_price + trend + noise)  # Minimum price
            
            # Create OHLC
            open_price = current_price
            volatility = abs(np.random.normal(0, base_volatility/2))
            
            high_price = open_price + abs(np.random.normal(0, volatility))
            low_price = open_price - abs(np.random.normal(0, volatility))
            close_price = open_price + np.random.normal(0, volatility/3)
            
            # Ensure OHLC logic
            high_price = max(open_price, high_price, low_price, close_price)
            low_price = min(open_price, high_price, low_price, close_price)
            
            volume = abs(np.random.normal(1000, 300))
            
            # Add some patterns occasionally
            if i % 50 == 0 and i > 0:  # Every 50 candles, add a hammer
                low_price = open_price - volatility * 3  # Long lower shadow
                close_price = open_price + volatility * 0.2
                volume *= 1.5  # Higher volume
                
            elif i % 75 == 0 and i > 0:  # Every 75 candles, add shooting star
                high_price = open_price + volatility * 3  # Long upper shadow
                close_price = open_price - volatility * 0.2
                volume *= 1.4
            
            data.append({
                'timestamp': date,
                'open': max(0.01, open_price),
                'high': max(0.01, high_price),
                'low': max(0.01, low_price),
                'close': max(0.01, close_price),
                'volume': max(1, volume)
            })
            
            current_price = close_price
        
        return pd.DataFrame(data)
    
    # Create both timeframes
    df_5m = create_candle_data(dates_5m, 50)
    df_1h = create_candle_data(dates_1h, 200)
    
    logger.info(f"Created {len(df_5m)} 5-minute candles and {len(df_1h)} 1-hour candles")
    
    return df_5m, df_1h

class MockCandlestickBacktestEngine(CandlestickBacktestEngine):
    """Mock version that doesn't fetch real data"""
    
    def fetch_historical_data(self, symbol: str, interval: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Override to return mock data instead of fetching from API"""
        try:
            self.logger.info(f"Using mock data for {symbol} {interval}")
            
            # Create mock data based on interval
            if interval == 'Hour1':
                dates = pd.date_range(start=start_time, end=end_time, freq='1H')
                base_volatility = 200
            else:  # Min5
                dates = pd.date_range(start=start_time, end=end_time, freq='5min')
                base_volatility = 50
            
            np.random.seed(42)  # Consistent data
            base_price = 50000
            current_price = base_price
            
            data = []
            for i, date in enumerate(dates):
                # Add trend and noise
                trend = i * 0.1
                noise = np.random.normal(0, base_volatility)
                current_price = max(1000, current_price + trend + noise)
                
                # Create OHLC
                open_price = current_price
                volatility = abs(np.random.normal(0, base_volatility/2))
                
                high_price = open_price + abs(np.random.normal(0, volatility))
                low_price = open_price - abs(np.random.normal(0, volatility))
                close_price = open_price + np.random.normal(0, volatility/3)
                
                # Ensure OHLC logic
                high_price = max(open_price, high_price, low_price, close_price)
                low_price = min(open_price, high_price, low_price, close_price)
                
                volume = abs(np.random.normal(1000, 300))
                
                # Add patterns occasionally
                if i % 100 == 0 and i > 0:  # Hammer pattern
                    low_price = open_price - volatility * 2.5
                    close_price = open_price + volatility * 0.1
                    volume *= 1.5
                elif i % 150 == 0 and i > 0:  # Shooting star
                    high_price = open_price + volatility * 2.5
                    close_price = open_price - volatility * 0.1
                    volume *= 1.4
                
                data.append({
                    'timestamp': date,
                    'open': max(0.01, open_price),
                    'high': max(0.01, high_price),
                    'low': max(0.01, low_price),
                    'close': max(0.01, close_price),
                    'volume': max(1, volume)
                })
                
                current_price = close_price
            
            df = pd.DataFrame(data)
            self.logger.info(f"Generated {len(df)} mock candles for {interval}")
            return df
            
        except Exception as e:
            self.logger.error(f"Error creating mock data: {e}")
            return pd.DataFrame()

def main():
    """Main function to run quick test backtest"""
    logger = get_logger(__name__)
    
    logger.info("="*80)
    logger.info("QUICK TEST CANDLESTICK PATTERN BACKTEST")
    logger.info("="*80)
    logger.info("Using mock data for faster testing")
    logger.info(f"Strategy Config: {CANDLESTICK_STRATEGY_CONFIG}")
    logger.info("="*80)
    
    # Use shorter date range for quick testing
    symbol = 'BTC_USDT'
    start_date = '2024-06-01'
    end_date = '2024-06-07'  # Just one week
    
    logger.info(f"Testing {symbol} from {start_date} to {end_date}")
    
    # Initialize mock backtest engine
    engine = MockCandlestickBacktestEngine()
    
    # Run backtest
    logger.info("Starting backtest with mock data...")
    results = engine.run_backtest(symbol, start_date, end_date)
    
    if 'error' in results:
        logger.error(f"Backtest failed: {results['error']}")
        return
    
    # Display results
    logger.info("\n" + "="*60)
    logger.info("QUICK TEST RESULTS")
    logger.info("="*60)
    
    logger.info(f"Symbol: {results['symbol']}")
    logger.info(f"Period: {results['start_date']} to {results['end_date']}")
    logger.info(f"Initial Capital: ${results['initial_capital']:,.2f}")
    logger.info(f"Final Capital: ${results['final_capital']:,.2f}")
    logger.info(f"Total Return: {results['total_return']:.2f}%")
    logger.info(f"Total Trades: {results['total_trades']}")
    
    if 'performance_metrics' in results:
        metrics = results['performance_metrics']
        logger.info("\nPERFORMANCE METRICS:")
        logger.info(f"Win Rate: {metrics.get('win_rate', 0):.2f}%")
        logger.info(f"Profit Factor: {metrics.get('profit_factor', 0):.2f}")
        logger.info(f"Average Win: ${metrics.get('avg_win', 0):.2f}")
        logger.info(f"Average Loss: ${metrics.get('avg_loss', 0):.2f}")
        logger.info(f"Max Drawdown: {metrics.get('max_drawdown', 0):.2f}%")
        logger.info(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
    
    # Pattern performance
    if 'pattern_performance' in results and results['pattern_performance']:
        logger.info("\nPATTERN PERFORMANCE:")
        for pattern_type, stats in results['pattern_performance'].items():
            logger.info(f"{pattern_type.upper()}:")
            logger.info(f"  Trades: {stats['trades']}")
            logger.info(f"  Win Rate: {stats['win_rate']:.2f}%")
            logger.info(f"  Avg PnL: ${stats['avg_pnl']:.2f}")
            logger.info(f"  Total PnL: ${stats['total_pnl']:.2f}")
    
    # Show some trades
    if results['trades']:
        logger.info(f"\nSAMPLE TRADES (showing first 5):")
        for i, trade in enumerate(results['trades'][:5]):
            logger.info(f"Trade {i+1}: {trade['side']} {trade['pattern_type']} - "
                       f"Entry: ${trade['entry_price']:.2f}, "
                       f"Exit: {trade.get('exit_reason', 'N/A')}, "
                       f"PnL: ${trade.get('pnl', 0):.2f}")
    
    logger.info("="*60)
    logger.info("Quick test completed successfully!")
    logger.info("If this works well, you can run the full backtest with real data.")

if __name__ == "__main__":
    main()
