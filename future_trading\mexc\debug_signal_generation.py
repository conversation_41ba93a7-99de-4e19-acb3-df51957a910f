#!/usr/bin/env python3
"""
Debug why patterns are detected but no trading signals are generated
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators
from config import CANDLESTICK_STRATEGY_CONFIG

def debug_signal_generation():
    """Debug the signal generation process step by step"""
    print("="*80)
    print("🔍 DEBUGGING SIGNAL GENERATION PROCESS")
    print("="*80)
    
    # Create test data
    dates = pd.date_range(start='2024-01-01', periods=500, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        price = base_price + i * 2 + np.random.normal(0, 30)
        
        # Create obvious hammer at index 250
        if i == 250:
            open_price = price
            high_price = price + 30
            low_price = price - 150  # Long lower shadow
            close_price = price + 20
            volume = 2000
            print(f"   📍 Embedded HAMMER pattern at index {i}")
        else:
            open_price = price
            high_price = price + abs(np.random.normal(0, 30))
            low_price = price - abs(np.random.normal(0, 30))
            close_price = price + np.random.normal(0, 15)
            volume = abs(np.random.normal(1000, 200))
        
        # Ensure OHLC logic
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': max(0.01, open_price),
            'high': max(0.01, high_price),
            'low': max(0.01, low_price),
            'close': max(0.01, close_price),
            'volume': max(1, volume)
        })
    
    df_5m = pd.DataFrame(data)
    
    # Create 1H data
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    
    # Add indicators to both timeframes
    indicators = TechnicalIndicators()
    
    for df in [df_5m, df_1h]:
        df['ema_50'] = indicators.ema(df['close'], 50)
        df['ema_200'] = indicators.ema(df['close'], 200)
        df['rsi'] = indicators.rsi(df['close'], 14)
        df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
        
        macd_data = indicators.macd(df['close'])
        df['macd'] = macd_data['macd']
        df['macd_signal'] = macd_data['signal']
        df['macd_histogram'] = macd_data['histogram']
        
        df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
        df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    print(f"   Created {len(df_5m)} 5M candles and {len(df_1h)} 1H candles")
    
    # Test at the hammer location
    test_index = 250
    print(f"\n🔍 DEBUGGING AT INDEX {test_index} (HAMMER LOCATION):")
    
    # Step 1: Test pattern detection
    patterns = CandlestickPatterns()
    pattern_result = patterns.detect_all_patterns(df_5m, test_index)
    
    print(f"\n1️⃣ PATTERN DETECTION:")
    print(f"   Has pattern: {pattern_result['has_pattern']}")
    if pattern_result['has_pattern']:
        strongest = pattern_result['strongest_pattern']
        print(f"   Strongest: {strongest['name']} ({strongest['signal']})")
        print(f"   Strength: {strongest['strength']:.3f}")
        print(f"   Threshold: {CANDLESTICK_STRATEGY_CONFIG['pattern_strength_threshold']}")
        print(f"   ✅ Pattern strength OK" if strongest['strength'] >= CANDLESTICK_STRATEGY_CONFIG['pattern_strength_threshold'] else "❌ Pattern too weak")
    
    # Step 2: Test market conditions
    strategy = CandlestickTradingStrategy()
    
    df_5m_slice = df_5m.iloc[:test_index+1].copy()
    h1_index = min(test_index // 12, len(df_1h) - 1)
    df_1h_slice = df_1h.iloc[:h1_index+1].copy()
    
    print(f"\n2️⃣ MARKET CONDITIONS:")
    print(f"   5M data length: {len(df_5m_slice)}")
    print(f"   1H data length: {len(df_1h_slice)}")
    
    market_conditions = strategy.validate_market_conditions(df_1h_slice, df_5m_slice)
    print(f"   Valid: {market_conditions.get('valid', False)}")
    print(f"   Reason: {market_conditions.get('reason', 'N/A')}")
    
    if market_conditions.get('valid'):
        print(f"   Trend: {market_conditions.get('trend_direction', 'unknown')}")
        print(f"   Volatility ratio: {market_conditions.get('volatility_ratio', 0):.4f}")
        print(f"   Volume ratio: {market_conditions.get('volume_ratio', 0):.2f}")
    else:
        print(f"   ❌ Market conditions failed!")
        
        # Debug market condition details
        if len(df_1h_slice) >= 2:
            latest_1h = df_1h_slice.iloc[-1]
            latest_5m = df_5m_slice.iloc[-1]
            
            print(f"\n   🔍 MARKET CONDITION DETAILS:")
            print(f"      1H ATR: {latest_1h.get('atr', 0):.2f}")
            print(f"      1H Price: {latest_1h['close']:.2f}")
            print(f"      Volatility ratio: {latest_1h.get('atr', 0) / latest_1h['close']:.4f}")
            print(f"      1H Volume: {latest_1h['volume']:.0f}")
            print(f"      1H Volume avg: {df_1h_slice['volume'].tail(20).mean():.0f}")
            print(f"      Volume ratio: {latest_1h['volume'] / df_1h_slice['volume'].tail(20).mean():.2f}")
    
    # Step 3: Test support/resistance analysis
    print(f"\n3️⃣ SUPPORT/RESISTANCE ANALYSIS:")
    sr_levels = strategy.analyze_support_resistance(df_5m_slice)
    print(f"   Support levels: {len(sr_levels['support'])}")
    print(f"   Resistance levels: {len(sr_levels['resistance'])}")
    
    if sr_levels['support']:
        print(f"   Support: {[f'{s:.2f}' for s in sr_levels['support'][:3]]}")
    if sr_levels['resistance']:
        print(f"   Resistance: {[f'{r:.2f}' for r in sr_levels['resistance'][:3]]}")
    
    # Step 4: Test pattern validation with context
    if pattern_result['has_pattern'] and market_conditions.get('valid'):
        print(f"\n4️⃣ PATTERN VALIDATION WITH CONTEXT:")
        
        pattern_validation = strategy.validate_pattern_with_context(
            pattern_result, df_1h_slice, df_5m_slice, sr_levels
        )
        
        print(f"   Valid: {pattern_validation.get('valid', False)}")
        print(f"   Reason: {pattern_validation.get('reason', 'N/A')}")
        
        if pattern_validation.get('valid'):
            print(f"   Context score: {pattern_validation.get('context_score', 0):.3f}")
            print(f"   Volume ratio: {pattern_validation.get('volume_ratio', 0):.2f}")
        else:
            print(f"   ❌ Pattern validation failed!")
            
            # Debug pattern validation details
            current_price = df_5m_slice.iloc[-1]['close']
            current_rsi = df_5m_slice.iloc[-1].get('rsi', 50)
            
            print(f"\n   🔍 PATTERN VALIDATION DETAILS:")
            print(f"      Current price: ${current_price:.2f}")
            print(f"      Current RSI: {current_rsi:.1f}")
            print(f"      RSI filter enabled: {CANDLESTICK_STRATEGY_CONFIG['rsi_filter_enabled']}")
            
            if pattern_result['strongest_pattern']['signal'] == 'bullish':
                print(f"      Bullish pattern - RSI should be < {CANDLESTICK_STRATEGY_CONFIG['rsi_bullish_threshold']}")
                print(f"      RSI check: {'✅ OK' if current_rsi < CANDLESTICK_STRATEGY_CONFIG['rsi_bullish_threshold'] else '❌ Failed'}")
            
            # Check S/R proximity
            buffer = CANDLESTICK_STRATEGY_CONFIG['support_resistance_buffer']
            if pattern_result['strongest_pattern']['signal'] == 'bullish':
                near_support = any(
                    abs(current_price - support) / support <= buffer 
                    for support in sr_levels['support']
                ) if sr_levels['support'] else True
                print(f"      Near support: {'✅ OK' if near_support else '❌ Failed'}")
    
    # Step 5: Full signal generation
    print(f"\n5️⃣ FULL SIGNAL GENERATION:")
    signal_data = strategy.generate_trading_signal(df_1h_slice, df_5m_slice, 'BTC_USDT')
    
    print(f"   Signal: {signal_data.get('signal', 'None')}")
    print(f"   Confidence: {signal_data.get('confidence', 0):.3f}")
    print(f"   Reason: {signal_data.get('reason', 'N/A')}")
    
    if signal_data.get('signal'):
        print(f"   ✅ SUCCESS! Signal generated")
        print(f"   Pattern: {signal_data.get('pattern_type', 'unknown')}")
        print(f"   Pattern strength: {signal_data.get('pattern_strength', 0):.3f}")
    else:
        print(f"   ❌ No signal generated")

def suggest_fixes():
    """Suggest fixes based on the debug results"""
    print(f"\n💡 SUGGESTED FIXES:")
    print(f"="*60)
    
    print(f"1. 🔧 RELAX MARKET CONDITIONS:")
    print(f"   - Increase volatility range acceptance")
    print(f"   - Lower volume requirements")
    print(f"   - Disable trend strength requirements")
    
    print(f"\n2. 🎯 SIMPLIFY PATTERN VALIDATION:")
    print(f"   - Disable RSI filter completely")
    print(f"   - Increase S/R buffer to 0.05 (5%)")
    print(f"   - Remove trend alignment requirements")
    
    print(f"\n3. 📉 FURTHER LOWER THRESHOLDS:")
    print(f"   - pattern_strength_threshold: 0.1")
    print(f"   - volume_multiplier: 0.8 (below average)")
    print(f"   - support_resistance_buffer: 0.05")

def main():
    """Main debug function"""
    debug_signal_generation()
    suggest_fixes()
    
    print(f"\n🚀 IMMEDIATE ACTION:")
    print(f"   Update config.py with even more relaxed parameters")
    print(f"   Focus on getting ANY trades first, then optimize quality")

if __name__ == "__main__":
    main()
