#!/usr/bin/env python3
"""
Hybrid Pattern Strategy - Combines real pattern detection with guaranteed trades
This ensures we get trades while still using actual market patterns when available
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List

from candlestick_trading_strategy import CandlestickTradingStrategy
from candlestick_patterns import CandlestickPatterns
from config import CANDLESTICK_STRATEGY_CONFIG, CAPITAL_USD, RISK_PER_TRADE
from logging_config import get_logger

class HybridPatternStrategy(CandlestickTradingStrategy):
    """Strategy that uses real patterns when available, otherwise generates trades based on simple conditions"""
    
    def __init__(self):
        super().__init__()
        self.trade_counter = 0
        self.last_trade_index = 0
        # OPTIMIZED: Less frequent, higher quality trades
        self.min_trade_interval = 100  # Increased from 50 (less frequent)
        self.max_trade_interval = 300  # Increased from 200 (more patience)

        # OPTIMIZED: Better risk management
        self.real_pattern_threshold = 0.4  # Increased from 0.3 (higher quality)
        self.trend_threshold = 0.02  # Increased from 0.01 (stronger trends)
        self.rsi_oversold_threshold = 30  # Decreased from 40 (more extreme)
        self.rsi_overbought_threshold = 70  # Increased from 60 (more extreme)
        
        self.logger.info("Hybrid Pattern Strategy initialized - Real patterns + guaranteed trades!")

    def validate_signal(self, signal_data: Dict) -> bool:
        """Validate signal data - always return True for hybrid strategy"""
        return (signal_data is not None and
                signal_data.get('signal') is not None and
                signal_data.get('levels') is not None)

    def generate_trading_signal(self, df_1h: pd.DataFrame, df_5m: pd.DataFrame,
                              symbol: str) -> Dict[str, any]:
        """Generate trading signals using hybrid approach"""
        try:
            # Basic data check
            if len(df_1h) < 10 or len(df_5m) < 50:
                return {
                    'signal': None,
                    'confidence': 0,
                    'reason': 'insufficient_data',
                    'timestamp': datetime.now(),
                    'symbol': symbol
                }
            
            current_index = len(df_5m)
            candles_since_last_trade = current_index - self.last_trade_index
            
            # First, try to detect real patterns
            pattern_data = self.patterns.detect_all_patterns(df_5m)
            
            if pattern_data['has_pattern']:
                strongest_pattern = pattern_data['strongest_pattern']

                # OPTIMIZED: Higher quality pattern threshold
                if (strongest_pattern['strength'] >= self.real_pattern_threshold and  # Higher quality
                    candles_since_last_trade >= self.min_trade_interval):
                    
                    self.trade_counter += 1
                    self.last_trade_index = current_index
                    
                    signal = 'BUY' if strongest_pattern['signal'] == 'bullish' else 'SELL'
                    
                    # Calculate levels
                    latest_5m = df_5m.iloc[-1]
                    entry_price = latest_5m['close']
                    atr = latest_5m.get('atr', entry_price * 0.02)
                    
                    levels = self.calculate_stop_loss_take_profit(
                        entry_price, signal, atr, strongest_pattern['type']
                    )
                    levels['entry_price'] = entry_price
                    levels['position_size'] = self.calculate_position_sizing(
                        entry_price, levels['stop_loss']
                    )
                    
                    self.logger.info(f"🎯 REAL PATTERN TRADE #{self.trade_counter}: {signal} "
                                   f"{strongest_pattern['type']} at ${entry_price:.2f} "
                                   f"(strength: {strongest_pattern['strength']:.3f})")
                    
                    return {
                        'signal': signal,
                        'confidence': strongest_pattern['strength'],
                        'pattern_type': strongest_pattern['type'],
                        'pattern_strength': strongest_pattern['strength'],
                        'levels': levels,
                        'timestamp': datetime.now(),
                        'symbol': symbol,
                        'trade_type': 'real_pattern',
                        'trade_number': self.trade_counter
                    }
            
            # If no real pattern, check if we should force a trade
            if candles_since_last_trade >= self.max_trade_interval:
                return self.generate_fallback_trade(df_1h, df_5m, symbol, current_index)
            
            # No trade this time
            return {
                'signal': None,
                'confidence': 0,
                'reason': f'waiting (patterns detected: {pattern_data.get("has_pattern", False)}, '
                         f'candles since last: {candles_since_last_trade})',
                'timestamp': datetime.now(),
                'symbol': symbol
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid signal generation: {e}")
            return {
                'signal': None,
                'confidence': 0,
                'error': str(e),
                'timestamp': datetime.now(),
                'symbol': symbol
            }
    
    def generate_fallback_trade(self, df_1h: pd.DataFrame, df_5m: pd.DataFrame, 
                               symbol: str, current_index: int) -> Dict[str, any]:
        """Generate fallback trade based on simple market conditions"""
        try:
            self.trade_counter += 1
            self.last_trade_index = current_index
            
            # Use simple trend-following logic
            latest_5m = df_5m.iloc[-1]
            entry_price = latest_5m['close']
            
            # OPTIMIZED trend detection with stronger requirements
            if len(df_5m) >= 20:
                price_20_ago = df_5m.iloc[-20]['close']
                price_change = (entry_price - price_20_ago) / price_20_ago

                # OPTIMIZED: Stronger trend requirements
                if price_change > self.trend_threshold:  # 2% up = BUY (was 1%)
                    signal = 'BUY'
                    pattern_type = 'uptrend_follow_optimized'
                elif price_change < -self.trend_threshold:  # 2% down = SELL (was 1%)
                    signal = 'SELL'
                    pattern_type = 'downtrend_follow_optimized'
                else:
                    # OPTIMIZED RSI with more extreme thresholds
                    rsi = latest_5m.get('rsi', 50)
                    if rsi < self.rsi_oversold_threshold:  # 30 (was 40)
                        signal = 'BUY'
                        pattern_type = 'rsi_oversold_optimized'
                    elif rsi > self.rsi_overbought_threshold:  # 70 (was 60)
                        signal = 'SELL'
                        pattern_type = 'rsi_overbought_optimized'
                    else:
                        # OPTIMIZED: Skip trade if conditions not strong enough
                        return {
                            'signal': None,
                            'confidence': 0,
                            'reason': 'optimized_conditions_not_met',
                            'timestamp': datetime.now(),
                            'symbol': symbol
                        }
            else:
                # Not enough data - alternate
                signal = 'BUY' if self.trade_counter % 2 == 1 else 'SELL'
                pattern_type = 'fallback_alternate'
            
            # Calculate levels
            atr = latest_5m.get('atr', entry_price * 0.02)
            levels = self.calculate_stop_loss_take_profit(
                entry_price, signal, atr, pattern_type
            )
            levels['entry_price'] = entry_price
            levels['position_size'] = self.calculate_position_sizing(
                entry_price, levels['stop_loss']
            )
            
            self.logger.info(f"📊 FALLBACK TRADE #{self.trade_counter}: {signal} "
                           f"{pattern_type} at ${entry_price:.2f}")
            
            return {
                'signal': signal,
                'confidence': 0.6,  # Medium confidence for fallback trades
                'pattern_type': pattern_type,
                'pattern_strength': 0.6,
                'levels': levels,
                'timestamp': datetime.now(),
                'symbol': symbol,
                'trade_type': 'fallback',
                'trade_number': self.trade_counter
            }
            
        except Exception as e:
            self.logger.error(f"Error generating fallback trade: {e}")
            return {
                'signal': None,
                'confidence': 0,
                'error': str(e),
                'timestamp': datetime.now(),
                'symbol': symbol
            }

class HybridBacktestEngine:
    """Simple test engine for hybrid strategy"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.strategy = HybridPatternStrategy()
    
    def quick_test(self, symbol: str = 'BTC_USDT'):
        """Quick test of hybrid strategy"""
        print("="*80)
        print("🔄 HYBRID PATTERN STRATEGY TEST")
        print("="*80)
        print("This strategy combines:")
        print("• Real candlestick patterns (when available)")
        print("• Fallback trades based on simple conditions")
        print("• Guaranteed trade generation every 50-200 candles")
        print("="*80)
        
        # Create test data
        dates = pd.date_range(start='2024-01-01', periods=1000, freq='5min')
        np.random.seed(42)
        
        base_price = 50000
        data = []
        
        for i in range(len(dates)):
            price = base_price + i * 1 + np.random.normal(0, 50)
            
            # Add some trend
            if i > 500:
                price += (i - 500) * 0.5  # Uptrend in second half
            
            open_price = price
            high_price = price + abs(np.random.normal(0, 30))
            low_price = price - abs(np.random.normal(0, 30))
            close_price = price + np.random.normal(0, 20)
            volume = abs(np.random.normal(1000, 200))
            
            # Ensure OHLC logic
            high_price = max(open_price, high_price, low_price, close_price)
            low_price = min(open_price, high_price, low_price, close_price)
            
            data.append({
                'timestamp': dates[i],
                'open': max(0.01, open_price),
                'high': max(0.01, high_price),
                'low': max(0.01, low_price),
                'close': max(0.01, close_price),
                'volume': max(1, volume)
            })
        
        df_5m = pd.DataFrame(data)
        df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
        
        # Add indicators
        from technical_indicators import TechnicalIndicators
        indicators = TechnicalIndicators()
        
        for df in [df_5m, df_1h]:
            df['ema_50'] = indicators.ema(df['close'], 50)
            df['ema_200'] = indicators.ema(df['close'], 200)
            df['rsi'] = indicators.rsi(df['close'], 14)
            df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
            df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
        
        print(f"📊 Testing with {len(df_5m)} 5M candles and {len(df_1h)} 1H candles")
        
        # Test signal generation
        signals_generated = 0
        real_patterns = 0
        fallback_trades = 0
        
        # Test every 25 candles
        for i in range(100, len(df_5m), 25):
            df_5m_slice = df_5m.iloc[:i+1].copy()
            h1_index = min(i // 12, len(df_1h) - 1)
            df_1h_slice = df_1h.iloc[:h1_index+1].copy()
            
            if len(df_1h_slice) >= 10:
                signal_data = self.strategy.generate_trading_signal(df_1h_slice, df_5m_slice, symbol)
                
                if signal_data.get('signal'):
                    signals_generated += 1
                    trade_type = signal_data.get('trade_type', 'unknown')
                    
                    if trade_type == 'real_pattern':
                        real_patterns += 1
                    elif trade_type == 'fallback':
                        fallback_trades += 1
                    
                    if signals_generated <= 5:  # Show first 5
                        print(f"   Signal {signals_generated}: {signal_data['signal']} "
                              f"{signal_data.get('pattern_type', 'unknown')} "
                              f"({trade_type}) at index {i}")
        
        print(f"\n📈 HYBRID STRATEGY RESULTS:")
        print(f"   Total signals: {signals_generated}")
        print(f"   Real patterns: {real_patterns}")
        print(f"   Fallback trades: {fallback_trades}")
        print(f"   Signal rate: {signals_generated / (len(range(100, len(df_5m), 25))) * 100:.1f}%")
        
        if signals_generated > 0:
            print(f"\n✅ SUCCESS! Hybrid strategy generates trades!")
            print(f"   🎯 {real_patterns} trades from real patterns")
            print(f"   📊 {fallback_trades} trades from fallback conditions")
        else:
            print(f"\n❌ No signals generated - check strategy logic")

if __name__ == "__main__":
    engine = HybridBacktestEngine()
    engine.quick_test()
