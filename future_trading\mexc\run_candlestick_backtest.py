#!/usr/bin/env python3
"""
Candlestick Pattern Trading Strategy Backtest Launcher
Based on comprehensive candlestick pattern analysis with MEXC API

This strategy focuses on:
1. Advanced candlestick pattern detection (<PERSON>, Shooting Star, Doji, Engulfing)
2. Pattern strength calculation based on volume, body ratio, and market context
3. Support/resistance level analysis for pattern validation
4. Multi-timeframe analysis (1H for trend, 5M for patterns)
5. Risk management with pattern-specific stop losses and take profits
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
import json
import time
from typing import Dict, List, Optional, Tuple

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from candlestick_backtest_engine import CandlestickBacktestEngine
from config import TRADING_PAIRS, CANDLESTICK_STRATEGY_CONFIG
from logging_config import get_logger

class EnhancedCandlestickBacktestEngine(CandlestickBacktestEngine):
    """Enhanced backtest engine with real-time progress display"""

    def __init__(self, verbose=False):
        super().__init__()
        self.verbose = verbose
        self.progress_counter = 0
        self.last_progress_time = time.time()
        self.start_time = None

    def print_progress(self, message, force=False):
        """Print progress message with timestamp"""
        if self.verbose or force:
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"[{current_time}] {message}")

    def print_trade_summary(self):
        """Print current trade summary"""
        if not self.trades:
            return

        total_trades = len(self.trades)
        winning_trades = sum(1 for trade in self.trades if trade.get('pnl', 0) > 0)
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0

        total_pnl = sum(trade.get('pnl', 0) for trade in self.trades)
        current_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100

        print(f"\n📈 CURRENT PERFORMANCE:")
        print(f"   Trades: {total_trades} | Wins: {winning_trades} | Losses: {losing_trades}")
        print(f"   Win Rate: {win_rate:.1f}% | Total PnL: ${total_pnl:.2f}")
        print(f"   Capital: ${self.current_capital:.2f} | Return: {current_return:.2f}%")

        # Show recent trades
        if len(self.trades) >= 3:
            print(f"   Recent trades:")
            for trade in self.trades[-3:]:
                pnl_emoji = "✅" if trade.get('pnl', 0) > 0 else "❌"
                print(f"     {pnl_emoji} {trade['pattern_type']} {trade['side']}: ${trade.get('pnl', 0):.2f}")

    def run_backtest(self, symbol: str, start_date: str, end_date: str) -> Dict[str, any]:
        """Enhanced run_backtest with progress display"""
        try:
            self.start_time = time.time()
            self.print_progress(f"🚀 Starting backtest for {symbol}", force=True)

            # Parse dates
            start_time = datetime.strptime(start_date, '%Y-%m-%d')
            end_time = datetime.strptime(end_date, '%Y-%m-%d')

            # Fetch historical data
            self.print_progress("📥 Fetching 1H data for trend analysis...", force=True)
            df_1h = self.fetch_historical_data(symbol, 'Hour1', start_time, end_time)

            self.print_progress("📥 Fetching 5M data for pattern detection...", force=True)
            df_5m = self.fetch_historical_data(symbol, 'Min5', start_time, end_time)

            if df_1h.empty or df_5m.empty:
                return {'error': 'Failed to fetch historical data'}

            self.print_progress(f"✅ Data fetched: {len(df_1h)} 1H candles, {len(df_5m)} 5M candles", force=True)

            # Prepare data with indicators
            self.print_progress("🔧 Adding technical indicators...", force=True)
            df_1h = self.prepare_data_with_indicators(df_1h)
            df_5m = self.prepare_data_with_indicators(df_5m)

            # Reset backtest state
            self.current_capital = self.initial_capital
            self.trades = []
            self.positions = {}
            self.equity_curve = []
            self.pattern_performance = {}

            # Run backtest simulation
            self.print_progress("🎯 Starting backtest simulation...", force=True)
            total_candles = len(df_5m)

            # Progress tracking
            progress_interval = max(100, total_candles // 50)  # Update every 2% or 100 candles

            for i in range(200, total_candles):
                current_time = df_5m.iloc[i]['timestamp']
                current_price = df_5m.iloc[i]['close']

                # Show progress
                if i % progress_interval == 0:
                    progress_pct = (i / total_candles) * 100
                    elapsed_time = time.time() - self.start_time
                    self.print_progress(f"📊 Progress: {progress_pct:.1f}% ({i}/{total_candles}) - "
                                      f"Elapsed: {elapsed_time:.0f}s - Active positions: {len(self.positions)}")

                    if self.trades:
                        self.print_trade_summary()

                # Get corresponding 1H data
                h1_index = self.find_closest_timestamp_index(df_1h, current_time)
                if h1_index < 50:
                    continue

                # Get data slices
                df_1h_slice = df_1h.iloc[:h1_index+1].copy()
                df_5m_slice = df_5m.iloc[:i+1].copy()

                # Check exit conditions for open positions
                positions_to_close = []
                for pos_id, position in self.positions.items():
                    exit_data = self.check_exit_conditions(position, current_price, current_time)
                    if exit_data:
                        positions_to_close.append((pos_id, exit_data))

                # Close positions
                for pos_id, exit_data in positions_to_close:
                    self.close_position(pos_id, exit_data)
                    if self.verbose:
                        pnl_emoji = "✅" if exit_data['pnl'] > 0 else "❌"
                        self.print_progress(f"{pnl_emoji} Closed position: {exit_data['exit_reason']} - "
                                          f"PnL: ${exit_data['pnl']:.2f}")

                # Generate new trading signals
                if len(self.positions) < 1:
                    signal_data = self.strategy.generate_trading_signal(
                        df_1h_slice, df_5m_slice, symbol
                    )

                    if signal_data['signal']:
                        trade = self.execute_trade(signal_data, current_time)
                        if trade:
                            self.print_progress(f"🎯 NEW {signal_data['signal']} SIGNAL: "
                                              f"{signal_data['pattern_type']} pattern - "
                                              f"Confidence: {signal_data['confidence']:.2f} - "
                                              f"Entry: ${trade['entry_price']:.2f}", force=True)

                # Update equity curve periodically
                if i % 100 == 0:
                    self.update_equity_curve(current_time)

            # Final processing
            self.print_progress("🏁 Finalizing backtest...", force=True)

            # Close remaining positions
            final_price = df_5m.iloc[-1]['close']
            final_time = df_5m.iloc[-1]['timestamp']

            for pos_id, position in list(self.positions.items()):
                if position['side'] == 'BUY':
                    exit_price = final_price * (1 - self.slippage)
                    pnl = (exit_price - position['entry_price']) * position['quantity']
                else:
                    exit_price = final_price * (1 + self.slippage)
                    pnl = (position['entry_price'] - exit_price) * position['quantity']

                exit_commission = exit_price * position['quantity'] * self.commission
                pnl -= exit_commission

                exit_data = {
                    'exit_reason': 'backtest_end',
                    'exit_price': exit_price,
                    'exit_time': final_time,
                    'pnl': pnl,
                    'exit_commission': exit_commission,
                    'partial_exit': False
                }

                self.close_position(pos_id, exit_data)

            # Final equity update
            self.update_equity_curve(final_time)

            # Calculate performance metrics
            performance = self.calculate_performance_metrics()

            # Final summary
            elapsed_time = time.time() - self.start_time
            self.print_progress(f"✅ Backtest completed in {elapsed_time:.1f} seconds!", force=True)

            # Show final results
            print(f"\n🎉 BACKTEST COMPLETED!")
            print(f"   Total trades: {len(self.trades)}")
            print(f"   Final capital: ${self.current_capital:.2f}")
            print(f"   Total return: {((self.current_capital - self.initial_capital) / self.initial_capital * 100):.2f}%")
            if performance.get('win_rate'):
                print(f"   Win rate: {performance['win_rate']:.1f}%")

            # Save results
            results = {
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date,
                'initial_capital': self.initial_capital,
                'final_capital': self.current_capital,
                'total_return': (self.current_capital - self.initial_capital) / self.initial_capital * 100,
                'total_trades': len(self.trades),
                'performance_metrics': performance,
                'pattern_performance': self.pattern_performance,
                'equity_curve': self.equity_curve,
                'trades': self.trades,
                'strategy_config': CANDLESTICK_STRATEGY_CONFIG,
                'backtest_completed': datetime.now().isoformat(),
                'backtest_duration_seconds': elapsed_time
            }

            # Save to file
            filename = f"candlestick_backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.print_progress(f"💾 Results saved to {filename}", force=True)

            return results

        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            return {'error': str(e)}

def main():
    """Main function to run candlestick pattern backtest"""
    logger = get_logger(__name__)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run Candlestick Pattern Trading Strategy Backtest')
    parser.add_argument('--symbol', type=str, default='BTC_USDT',
                       choices=TRADING_PAIRS, help='Trading symbol')
    parser.add_argument('--start-date', type=str, default='2024-01-01',
                       help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2024-12-31',
                       help='End date (YYYY-MM-DD)')
    parser.add_argument('--optimize', action='store_true',
                       help='Run parameter optimization')
    parser.add_argument('--analyze-patterns', action='store_true',
                       help='Analyze individual pattern performance')
    parser.add_argument('--verbose', action='store_true',
                       help='Show detailed progress logs')

    args = parser.parse_args()

    print("="*80)
    print("CANDLESTICK PATTERN TRADING STRATEGY BACKTEST")
    print("="*80)
    print(f"Symbol: {args.symbol}")
    print(f"Period: {args.start_date} to {args.end_date}")
    print(f"Verbose logging: {args.verbose}")
    print("="*80)

    # Show current configuration
    print("\nCURRENT STRATEGY CONFIGURATION:")
    for key, value in CANDLESTICK_STRATEGY_CONFIG.items():
        print(f"  {key}: {value}")
    print("="*80)

    # Initialize backtest engine with verbose logging
    engine = EnhancedCandlestickBacktestEngine(verbose=args.verbose)

    if args.optimize:
        print("\n🔧 RUNNING PARAMETER OPTIMIZATION...")
        results = run_optimization(engine, args.symbol, args.start_date, args.end_date)
    else:
        print(f"\n📊 RUNNING BACKTEST FOR {args.symbol}...")
        results = engine.run_backtest(args.symbol, args.start_date, args.end_date)

    if 'error' in results:
        print(f"\n❌ BACKTEST FAILED: {results['error']}")
        logger.error(f"Backtest failed: {results['error']}")
        return

    # Display results
    display_results(results, logger)

    if args.analyze_patterns:
        analyze_pattern_performance(results, logger)

def run_optimization(engine, symbol, start_date, end_date):
    """Run parameter optimization for candlestick strategy"""
    logger = get_logger(__name__)
    
    # Define parameter ranges to optimize
    optimization_params = {
        'pattern_strength_threshold': [0.6, 0.7, 0.8, 0.9],
        'volume_multiplier': [1.2, 1.5, 1.8, 2.0],
        'rsi_bullish_threshold': [35, 40, 45],
        'rsi_bearish_threshold': [55, 60, 65],
        'atr_multiplier_sl': [1.0, 1.2, 1.5, 1.8],
        'atr_multiplier_tp1': [2.0, 2.5, 3.0, 3.5],
        'atr_multiplier_tp2': [3.5, 4.0, 4.5, 5.0]
    }
    
    best_result = None
    best_return = -float('inf')
    optimization_results = []
    
    # Get original config
    original_config = CANDLESTICK_STRATEGY_CONFIG.copy()
    
    logger.info("Starting parameter optimization...")
    logger.info(f"Total combinations to test: {calculate_combinations(optimization_params)}")
    
    combination_count = 0
    
    # Test different parameter combinations
    for strength_threshold in optimization_params['pattern_strength_threshold']:
        for volume_mult in optimization_params['volume_multiplier']:
            for rsi_bull in optimization_params['rsi_bullish_threshold']:
                for rsi_bear in optimization_params['rsi_bearish_threshold']:
                    for atr_sl in optimization_params['atr_multiplier_sl']:
                        for atr_tp1 in optimization_params['atr_multiplier_tp1']:
                            for atr_tp2 in optimization_params['atr_multiplier_tp2']:
                                
                                # Skip invalid combinations
                                if rsi_bull >= rsi_bear or atr_tp1 >= atr_tp2:
                                    continue
                                
                                combination_count += 1
                                
                                # Update strategy config
                                test_config = original_config.copy()
                                test_config.update({
                                    'pattern_strength_threshold': strength_threshold,
                                    'volume_multiplier': volume_mult,
                                    'rsi_bullish_threshold': rsi_bull,
                                    'rsi_bearish_threshold': rsi_bear,
                                    'atr_multiplier_sl': atr_sl,
                                    'atr_multiplier_tp1': atr_tp1,
                                    'atr_multiplier_tp2': atr_tp2
                                })
                                
                                # Update global config (this is a bit hacky but works for testing)
                                import config
                                config.CANDLESTICK_STRATEGY_CONFIG = test_config
                                
                                # Create new engine with updated config
                                test_engine = CandlestickBacktestEngine()
                                
                                logger.info(f"Testing combination {combination_count}: "
                                          f"ST={strength_threshold}, VM={volume_mult}, "
                                          f"RSI={rsi_bull}/{rsi_bear}, ATR={atr_sl}/{atr_tp1}/{atr_tp2}")
                                
                                # Run backtest
                                result = test_engine.run_backtest(symbol, start_date, end_date)
                                
                                if 'error' not in result:
                                    total_return = result['total_return']
                                    win_rate = result['performance_metrics'].get('win_rate', 0)
                                    total_trades = result['total_trades']
                                    
                                    # Calculate optimization score (weighted combination of metrics)
                                    if total_trades >= 10:  # Minimum trades for valid result
                                        score = (total_return * 0.4 + 
                                               win_rate * 0.3 + 
                                               min(total_trades / 50, 1.0) * 0.3)
                                    else:
                                        score = -1000  # Penalize low trade count
                                    
                                    optimization_results.append({
                                        'config': test_config.copy(),
                                        'total_return': total_return,
                                        'win_rate': win_rate,
                                        'total_trades': total_trades,
                                        'score': score,
                                        'performance_metrics': result['performance_metrics']
                                    })
                                    
                                    if score > best_return:
                                        best_return = score
                                        best_result = result.copy()
                                        best_result['optimization_config'] = test_config.copy()
                                        
                                        logger.info(f"New best result! Score: {score:.2f}, "
                                                  f"Return: {total_return:.2f}%, "
                                                  f"Win Rate: {win_rate:.2f}%, "
                                                  f"Trades: {total_trades}")
    
    # Restore original config
    import config
    config.CANDLESTICK_STRATEGY_CONFIG = original_config
    
    # Save optimization results
    optimization_summary = {
        'best_result': best_result,
        'all_results': sorted(optimization_results, key=lambda x: x['score'], reverse=True)[:20],  # Top 20
        'optimization_completed': datetime.now().isoformat(),
        'total_combinations_tested': combination_count
    }
    
    filename = f"candlestick_optimization_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(optimization_summary, f, indent=2, default=str)
    
    logger.info(f"Optimization completed. Results saved to {filename}")
    logger.info(f"Best configuration found with score: {best_return:.2f}")
    
    return best_result if best_result else {'error': 'No valid results found'}

def calculate_combinations(params):
    """Calculate total number of parameter combinations"""
    total = 1
    for param_list in params.values():
        total *= len(param_list)
    return total

def display_results(results, logger):
    """Display backtest results in a formatted way"""
    print("\n" + "="*80)
    print("📊 DETAILED BACKTEST RESULTS")
    print("="*80)

    # Basic info
    print(f"🎯 Symbol: {results['symbol']}")
    print(f"📅 Period: {results['start_date']} to {results['end_date']}")
    print(f"⏱️  Duration: {results.get('backtest_duration_seconds', 0):.1f} seconds")
    print(f"💰 Initial Capital: ${results['initial_capital']:,.2f}")
    print(f"💰 Final Capital: ${results['final_capital']:,.2f}")

    # Performance overview
    total_return = results['total_return']
    return_emoji = "📈" if total_return > 0 else "📉" if total_return < 0 else "➡️"
    print(f"{return_emoji} Total Return: {total_return:.2f}%")
    print(f"🔢 Total Trades: {results['total_trades']}")

    if 'performance_metrics' in results and results['total_trades'] > 0:
        metrics = results['performance_metrics']
        print(f"\n📈 PERFORMANCE METRICS:")

        # Win rate with emoji
        win_rate = metrics.get('win_rate', 0)
        win_emoji = "🎯" if win_rate >= 60 else "⚡" if win_rate >= 50 else "⚠️"
        print(f"   {win_emoji} Win Rate: {win_rate:.2f}%")

        # Profit factor
        pf = metrics.get('profit_factor', 0)
        pf_emoji = "🚀" if pf >= 2.0 else "✅" if pf >= 1.5 else "⚠️" if pf >= 1.0 else "❌"
        print(f"   {pf_emoji} Profit Factor: {pf:.2f}")

        print(f"   💵 Average Win: ${metrics.get('avg_win', 0):.2f}")
        print(f"   💸 Average Loss: ${metrics.get('avg_loss', 0):.2f}")

        # Max drawdown
        dd = metrics.get('max_drawdown', 0)
        dd_emoji = "✅" if dd <= 5 else "⚠️" if dd <= 15 else "❌"
        print(f"   {dd_emoji} Max Drawdown: {dd:.2f}%")

        print(f"   📊 Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")

        # Additional metrics
        print(f"   🏆 Winning Trades: {metrics.get('winning_trades', 0)}")
        print(f"   💔 Losing Trades: {metrics.get('losing_trades', 0)}")
        print(f"   💰 Gross Profit: ${metrics.get('gross_profit', 0):.2f}")
        print(f"   💸 Gross Loss: ${metrics.get('gross_loss', 0):.2f}")

    # Pattern performance
    if 'pattern_performance' in results and results['pattern_performance']:
        print(f"\n🎨 PATTERN PERFORMANCE:")
        for pattern_type, stats in results['pattern_performance'].items():
            pattern_emoji = get_pattern_emoji(pattern_type)
            win_rate = stats.get('win_rate', 0)
            win_emoji = "🎯" if win_rate >= 60 else "⚡" if win_rate >= 50 else "⚠️"

            print(f"   {pattern_emoji} {pattern_type.upper()}:")
            print(f"      {win_emoji} Win Rate: {win_rate:.1f}% ({stats.get('wins', 0)}/{stats.get('trades', 0)})")
            print(f"      💰 Total PnL: ${stats.get('total_pnl', 0):.2f}")
            print(f"      📊 Avg PnL: ${stats.get('avg_pnl', 0):.2f}")

    # Recent trades sample
    if 'trades' in results and results['trades']:
        print(f"\n📋 RECENT TRADES (Last 10):")
        recent_trades = results['trades'][-10:]
        for i, trade in enumerate(recent_trades, 1):
            pnl = trade.get('pnl', 0)
            pnl_emoji = "✅" if pnl > 0 else "❌" if pnl < 0 else "➡️"
            pattern_emoji = get_pattern_emoji(trade.get('pattern_type', 'unknown'))

            print(f"   {i:2d}. {pnl_emoji} {pattern_emoji} {trade.get('pattern_type', 'unknown')} "
                  f"{trade.get('side', 'N/A')} - Entry: ${trade.get('entry_price', 0):.2f} - "
                  f"Exit: {trade.get('exit_reason', 'N/A')} - PnL: ${pnl:.2f}")

    print("="*80)

def get_pattern_emoji(pattern_type):
    """Get emoji for pattern type"""
    pattern_emojis = {
        'hammer': '🔨',
        'shooting_star': '⭐',
        'doji': '🎯',
        'engulfing': '🌊',
        'unknown': '❓'
    }
    return pattern_emojis.get(pattern_type.lower(), '❓')

def analyze_pattern_performance(results, logger):
    """Analyze performance of individual candlestick patterns"""
    print("\n" + "="*80)
    print("🎨 DETAILED PATTERN PERFORMANCE ANALYSIS")
    print("="*80)

    if 'pattern_performance' in results and results['pattern_performance']:
        # Sort patterns by total PnL
        sorted_patterns = sorted(
            results['pattern_performance'].items(),
            key=lambda x: x[1].get('total_pnl', 0),
            reverse=True
        )

        print("📊 PATTERNS RANKED BY PROFITABILITY:")
        print("-" * 80)

        for rank, (pattern_type, stats) in enumerate(sorted_patterns, 1):
            pattern_emoji = get_pattern_emoji(pattern_type)
            win_rate = stats.get('win_rate', 0)
            total_pnl = stats.get('total_pnl', 0)
            trades = stats.get('trades', 0)
            wins = stats.get('wins', 0)

            # Performance indicators
            win_emoji = "🎯" if win_rate >= 60 else "⚡" if win_rate >= 50 else "⚠️"
            pnl_emoji = "💰" if total_pnl > 0 else "💸" if total_pnl < 0 else "➡️"

            print(f"\n{rank}. {pattern_emoji} {pattern_type.upper()} PATTERN:")
            print(f"   {win_emoji} Win Rate: {win_rate:.1f}% ({wins}/{trades} trades)")
            print(f"   {pnl_emoji} Total PnL: ${total_pnl:.2f}")
            print(f"   📊 Average PnL: ${stats.get('avg_pnl', 0):.2f}")

            if trades > 0:
                # Calculate additional stats
                avg_win = stats.get('total_pnl', 0) / wins if wins > 0 else 0
                avg_loss = stats.get('total_pnl', 0) / (trades - wins) if (trades - wins) > 0 else 0

                if wins > 0:
                    print(f"   ✅ Average Win: ${avg_win:.2f}")
                if (trades - wins) > 0:
                    print(f"   ❌ Average Loss: ${avg_loss:.2f}")

                # Profit factor for this pattern
                if avg_loss < 0:
                    pattern_pf = abs(avg_win * wins / (avg_loss * (trades - wins)))
                    pf_emoji = "🚀" if pattern_pf >= 2.0 else "✅" if pattern_pf >= 1.5 else "⚠️"
                    print(f"   {pf_emoji} Profit Factor: {pattern_pf:.2f}")

        # Pattern recommendations
        print(f"\n🎯 PATTERN RECOMMENDATIONS:")
        print("-" * 80)

        best_patterns = [p for p, s in sorted_patterns if s.get('win_rate', 0) >= 60 and s.get('total_pnl', 0) > 0]
        worst_patterns = [p for p, s in sorted_patterns if s.get('win_rate', 0) < 40 or s.get('total_pnl', 0) < 0]

        if best_patterns:
            print("✅ STRONG PATTERNS (Consider focusing on these):")
            for pattern in best_patterns[:3]:  # Top 3
                pattern_emoji = get_pattern_emoji(pattern)
                print(f"   {pattern_emoji} {pattern.upper()}")

        if worst_patterns:
            print("\n⚠️  WEAK PATTERNS (Consider avoiding or improving filters):")
            for pattern in worst_patterns[-3:]:  # Bottom 3
                pattern_emoji = get_pattern_emoji(pattern)
                print(f"   {pattern_emoji} {pattern.upper()}")

    else:
        print("❌ No pattern performance data available")

    print("="*80)

if __name__ == "__main__":
    main()
