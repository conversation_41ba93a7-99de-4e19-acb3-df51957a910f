#!/usr/bin/env python3
"""
Candlestick Pattern Trading Strategy Backtest Launcher
Based on comprehensive candlestick pattern analysis with MEXC API

This strategy focuses on:
1. Advanced candlestick pattern detection (<PERSON>, Shooting Star, Doji, Engulfing)
2. Pattern strength calculation based on volume, body ratio, and market context
3. Support/resistance level analysis for pattern validation
4. Multi-timeframe analysis (1H for trend, 5M for patterns)
5. Risk management with pattern-specific stop losses and take profits
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from candlestick_backtest_engine import CandlestickBacktestEngine
from config import TRADING_PAIRS, CANDLESTICK_STRATEGY_CONFIG
from logging_config import get_logger

def main():
    """Main function to run candlestick pattern backtest"""
    logger = get_logger(__name__)
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run Candlestick Pattern Trading Strategy Backtest')
    parser.add_argument('--symbol', type=str, default='BTC_USDT', 
                       choices=TRADING_PAIRS, help='Trading symbol')
    parser.add_argument('--start-date', type=str, default='2024-01-01', 
                       help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2024-12-31', 
                       help='End date (YYYY-MM-DD)')
    parser.add_argument('--optimize', action='store_true', 
                       help='Run parameter optimization')
    parser.add_argument('--analyze-patterns', action='store_true',
                       help='Analyze individual pattern performance')
    
    args = parser.parse_args()
    
    logger.info("="*80)
    logger.info("CANDLESTICK PATTERN TRADING STRATEGY BACKTEST")
    logger.info("="*80)
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Period: {args.start_date} to {args.end_date}")
    logger.info(f"Strategy Config: {CANDLESTICK_STRATEGY_CONFIG}")
    logger.info("="*80)
    
    # Initialize backtest engine
    engine = CandlestickBacktestEngine()
    
    if args.optimize:
        logger.info("Running parameter optimization...")
        results = run_optimization(engine, args.symbol, args.start_date, args.end_date)
    else:
        logger.info("Running single backtest...")
        results = engine.run_backtest(args.symbol, args.start_date, args.end_date)
    
    if 'error' in results:
        logger.error(f"Backtest failed: {results['error']}")
        return
    
    # Display results
    display_results(results, logger)
    
    if args.analyze_patterns:
        analyze_pattern_performance(results, logger)

def run_optimization(engine, symbol, start_date, end_date):
    """Run parameter optimization for candlestick strategy"""
    logger = get_logger(__name__)
    
    # Define parameter ranges to optimize
    optimization_params = {
        'pattern_strength_threshold': [0.6, 0.7, 0.8, 0.9],
        'volume_multiplier': [1.2, 1.5, 1.8, 2.0],
        'rsi_bullish_threshold': [35, 40, 45],
        'rsi_bearish_threshold': [55, 60, 65],
        'atr_multiplier_sl': [1.0, 1.2, 1.5, 1.8],
        'atr_multiplier_tp1': [2.0, 2.5, 3.0, 3.5],
        'atr_multiplier_tp2': [3.5, 4.0, 4.5, 5.0]
    }
    
    best_result = None
    best_return = -float('inf')
    optimization_results = []
    
    # Get original config
    original_config = CANDLESTICK_STRATEGY_CONFIG.copy()
    
    logger.info("Starting parameter optimization...")
    logger.info(f"Total combinations to test: {calculate_combinations(optimization_params)}")
    
    combination_count = 0
    
    # Test different parameter combinations
    for strength_threshold in optimization_params['pattern_strength_threshold']:
        for volume_mult in optimization_params['volume_multiplier']:
            for rsi_bull in optimization_params['rsi_bullish_threshold']:
                for rsi_bear in optimization_params['rsi_bearish_threshold']:
                    for atr_sl in optimization_params['atr_multiplier_sl']:
                        for atr_tp1 in optimization_params['atr_multiplier_tp1']:
                            for atr_tp2 in optimization_params['atr_multiplier_tp2']:
                                
                                # Skip invalid combinations
                                if rsi_bull >= rsi_bear or atr_tp1 >= atr_tp2:
                                    continue
                                
                                combination_count += 1
                                
                                # Update strategy config
                                test_config = original_config.copy()
                                test_config.update({
                                    'pattern_strength_threshold': strength_threshold,
                                    'volume_multiplier': volume_mult,
                                    'rsi_bullish_threshold': rsi_bull,
                                    'rsi_bearish_threshold': rsi_bear,
                                    'atr_multiplier_sl': atr_sl,
                                    'atr_multiplier_tp1': atr_tp1,
                                    'atr_multiplier_tp2': atr_tp2
                                })
                                
                                # Update global config (this is a bit hacky but works for testing)
                                import config
                                config.CANDLESTICK_STRATEGY_CONFIG = test_config
                                
                                # Create new engine with updated config
                                test_engine = CandlestickBacktestEngine()
                                
                                logger.info(f"Testing combination {combination_count}: "
                                          f"ST={strength_threshold}, VM={volume_mult}, "
                                          f"RSI={rsi_bull}/{rsi_bear}, ATR={atr_sl}/{atr_tp1}/{atr_tp2}")
                                
                                # Run backtest
                                result = test_engine.run_backtest(symbol, start_date, end_date)
                                
                                if 'error' not in result:
                                    total_return = result['total_return']
                                    win_rate = result['performance_metrics'].get('win_rate', 0)
                                    total_trades = result['total_trades']
                                    
                                    # Calculate optimization score (weighted combination of metrics)
                                    if total_trades >= 10:  # Minimum trades for valid result
                                        score = (total_return * 0.4 + 
                                               win_rate * 0.3 + 
                                               min(total_trades / 50, 1.0) * 0.3)
                                    else:
                                        score = -1000  # Penalize low trade count
                                    
                                    optimization_results.append({
                                        'config': test_config.copy(),
                                        'total_return': total_return,
                                        'win_rate': win_rate,
                                        'total_trades': total_trades,
                                        'score': score,
                                        'performance_metrics': result['performance_metrics']
                                    })
                                    
                                    if score > best_return:
                                        best_return = score
                                        best_result = result.copy()
                                        best_result['optimization_config'] = test_config.copy()
                                        
                                        logger.info(f"New best result! Score: {score:.2f}, "
                                                  f"Return: {total_return:.2f}%, "
                                                  f"Win Rate: {win_rate:.2f}%, "
                                                  f"Trades: {total_trades}")
    
    # Restore original config
    import config
    config.CANDLESTICK_STRATEGY_CONFIG = original_config
    
    # Save optimization results
    optimization_summary = {
        'best_result': best_result,
        'all_results': sorted(optimization_results, key=lambda x: x['score'], reverse=True)[:20],  # Top 20
        'optimization_completed': datetime.now().isoformat(),
        'total_combinations_tested': combination_count
    }
    
    filename = f"candlestick_optimization_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(optimization_summary, f, indent=2, default=str)
    
    logger.info(f"Optimization completed. Results saved to {filename}")
    logger.info(f"Best configuration found with score: {best_return:.2f}")
    
    return best_result if best_result else {'error': 'No valid results found'}

def calculate_combinations(params):
    """Calculate total number of parameter combinations"""
    total = 1
    for param_list in params.values():
        total *= len(param_list)
    return total

def display_results(results, logger):
    """Display backtest results in a formatted way"""
    logger.info("\n" + "="*60)
    logger.info("BACKTEST RESULTS SUMMARY")
    logger.info("="*60)
    
    logger.info(f"Symbol: {results['symbol']}")
    logger.info(f"Period: {results['start_date']} to {results['end_date']}")
    logger.info(f"Initial Capital: ${results['initial_capital']:,.2f}")
    logger.info(f"Final Capital: ${results['final_capital']:,.2f}")
    logger.info(f"Total Return: {results['total_return']:.2f}%")
    logger.info(f"Total Trades: {results['total_trades']}")
    
    if 'performance_metrics' in results:
        metrics = results['performance_metrics']
        logger.info("\nPERFORMANCE METRICS:")
        logger.info(f"Win Rate: {metrics.get('win_rate', 0):.2f}%")
        logger.info(f"Profit Factor: {metrics.get('profit_factor', 0):.2f}")
        logger.info(f"Average Win: ${metrics.get('avg_win', 0):.2f}")
        logger.info(f"Average Loss: ${metrics.get('avg_loss', 0):.2f}")
        logger.info(f"Max Drawdown: {metrics.get('max_drawdown', 0):.2f}%")
        logger.info(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
    
    logger.info("="*60)

def analyze_pattern_performance(results, logger):
    """Analyze performance of individual candlestick patterns"""
    logger.info("\n" + "="*60)
    logger.info("PATTERN PERFORMANCE ANALYSIS")
    logger.info("="*60)
    
    if 'pattern_performance' in results:
        for pattern_type, stats in results['pattern_performance'].items():
            logger.info(f"\n{pattern_type.upper()} PATTERN:")
            logger.info(f"  Total Trades: {stats['trades']}")
            logger.info(f"  Win Rate: {stats['win_rate']:.2f}%")
            logger.info(f"  Average PnL: ${stats['avg_pnl']:.2f}")
            logger.info(f"  Total PnL: ${stats['total_pnl']:.2f}")
    
    logger.info("="*60)

if __name__ == "__main__":
    main()
