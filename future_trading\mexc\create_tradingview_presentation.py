#!/usr/bin/env python3
"""
Create TradingView-style interactive HTML presentation with entry/exit markers and stacked indicators
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from technical_indicators import TechnicalIndicators

def load_backtest_results():
    """Load the most recent backtest results"""
    try:
        backtest_files = [f for f in os.listdir('.') if f.startswith('candlestick_backtest_') and f.endswith('.json')]
        if not backtest_files:
            print("❌ No backtest results found")
            return None
        
        latest_file = max(backtest_files, key=os.path.getctime)
        print(f"📊 Loading backtest results from: {latest_file}")
        
        with open(latest_file, 'r') as f:
            results = json.load(f)
        
        return results, latest_file
    except Exception as e:
        print(f"❌ Error loading backtest results: {e}")
        return None

def create_synthetic_market_data(start_date, end_date, trades):
    """Create synthetic market data for presentation"""
    try:
        print(f"📊 Creating synthetic market data...")
        
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        dates_5m = pd.date_range(start=start, end=end, freq='5min')
        
        base_price = 95000
        data_5m = []
        
        np.random.seed(42)
        
        for i, timestamp in enumerate(dates_5m):
            trend = (i / len(dates_5m)) * 2000
            noise = np.random.normal(0, 200)
            price = base_price + trend + noise
            
            open_price = price + np.random.normal(0, 50)
            high_price = max(open_price, price) + abs(np.random.normal(0, 100))
            low_price = min(open_price, price) - abs(np.random.normal(0, 100))
            close_price = price + np.random.normal(0, 30)
            
            high_price = max(open_price, high_price, low_price, close_price)
            low_price = min(open_price, high_price, low_price, close_price)
            volume = abs(np.random.normal(1000, 300))
            
            data_5m.append({
                'timestamp': timestamp.isoformat(),
                'open': max(1, open_price),
                'high': max(1, high_price),
                'low': max(1, low_price),
                'close': max(1, close_price),
                'volume': max(1, volume)
            })
        
        df_5m = pd.DataFrame(data_5m)
        df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
        
        # Add technical indicators
        indicators = TechnicalIndicators()
        
        for df in [df_5m, df_1h]:
            df['ema_50'] = indicators.ema(df['close'], 50)
            df['ema_200'] = indicators.ema(df['close'], 200)
            df['rsi'] = indicators.rsi(df['close'], 14)
            df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
            df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = indicators.bollinger_bands(df['close'], 20, 2)
            df['macd'], df['macd_signal'], df['macd_histogram'] = indicators.macd(df['close'])
        
        # Adjust trade timestamps and prices
        if trades:
            available_timestamps = df_5m['timestamp'].tolist()
            for trade in trades:
                trade_time = datetime.fromisoformat(trade.get('entry_time', start.isoformat()))
                closest_idx = min(range(len(available_timestamps)), 
                                key=lambda i: abs(datetime.fromisoformat(available_timestamps[i]) - trade_time))
                trade['timestamp'] = available_timestamps[closest_idx]
                
                closest_price = df_5m.iloc[closest_idx]['close']
                trade['entry_price'] = closest_price + np.random.normal(0, 50)
                trade['exit_price'] = trade['entry_price'] + trade.get('pnl', 0) / 0.01
                
                # Add exit timestamp (assume trade duration)
                exit_idx = min(closest_idx + trade.get('duration_minutes', 60) // 5, len(available_timestamps) - 1)
                trade['exit_timestamp'] = available_timestamps[exit_idx]
        
        print(f"✅ Created {len(df_5m)} 5M candles and {len(df_1h)} 1H candles")
        return df_5m, df_1h
        
    except Exception as e:
        print(f"❌ Error creating synthetic data: {e}")
        return None, None

def create_tradingview_html(results, df_5m, df_1h, filename):
    """Create TradingView-style HTML presentation"""
    
    total_trades = results.get('total_trades', 0)
    win_rate = results.get('performance_metrics', {}).get('win_rate', 0)
    total_return = results.get('total_return', 0)
    final_capital = results.get('final_capital', 1000)
    trades = results.get('trades', [])
    
    # Prepare trade data
    trade_data = []
    for i, trade in enumerate(trades):
        trade_data.append({
            'id': i + 1,
            'entry_timestamp': trade.get('timestamp', trade.get('entry_time', '')),
            'exit_timestamp': trade.get('exit_timestamp', trade.get('timestamp', '')),
            'side': trade.get('side', ''),
            'pattern': trade.get('pattern_type', ''),
            'entry_price': trade.get('entry_price', 0),
            'exit_price': trade.get('exit_price', 0),
            'stop_loss': trade.get('stop_loss', 0),
            'take_profit_1': trade.get('take_profit_1', 0),
            'take_profit_2': trade.get('take_profit_2', 0),
            'exit_reason': trade.get('exit_reason', ''),
            'pnl': trade.get('pnl', 0),
            'duration': trade.get('duration_minutes', 0)
        })
    
    df_5m_json = df_5m.to_json(orient='records', date_format='iso')
    df_1h_json = df_1h.to_json(orient='records', date_format='iso')
    trades_json = json.dumps(trade_data)
    
    html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Trading Strategy Analysis - TradingView Style</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e222d;
            color: #d1d4dc;
            line-height: 1.6;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1600px;
            margin: 0 auto;
            padding: 10px;
        }}
        
        .header {{
            background: #2a2e39;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #363a45;
        }}
        
        .header h1 {{
            color: #ffffff;
            font-size: 1.8em;
            margin-bottom: 8px;
        }}
        
        .subtitle {{
            color: #868993;
            font-size: 0.9em;
        }}
        
        .metrics-bar {{
            display: flex;
            justify-content: space-around;
            background: #2a2e39;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #363a45;
        }}
        
        .metric {{
            text-align: center;
        }}
        
        .metric-value {{
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 3px;
        }}
        
        .metric-label {{
            color: #868993;
            font-size: 0.8em;
            text-transform: uppercase;
        }}
        
        .positive {{ color: #00d4aa; }}
        .negative {{ color: #ff6b6b; }}
        .neutral {{ color: #2196f3; }}
        
        .chart-container {{
            background: #2a2e39;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #363a45;
        }}
        
        .chart-title {{
            color: #ffffff;
            font-size: 1.2em;
            margin-bottom: 15px;
            font-weight: 600;
        }}
        
        .controls {{
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }}
        
        .control-btn {{
            background: #363a45;
            color: #d1d4dc;
            border: 1px solid #4a4e5a;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            transition: all 0.2s ease;
        }}
        
        .control-btn:hover {{
            background: #4a4e5a;
            border-color: #5a5e6a;
        }}
        
        .control-btn.active {{
            background: #00d4aa;
            color: #1e222d;
            border-color: #00d4aa;
        }}
        
        .main-chart {{
            height: 400px;
            margin-bottom: 5px;
        }}
        
        .indicator-chart {{
            height: 120px;
            margin-bottom: 5px;
        }}
        
        .trades-section {{
            background: #2a2e39;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid #363a45;
        }}
        
        .trades-table {{
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85em;
        }}
        
        .trades-table th,
        .trades-table td {{
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #363a45;
        }}
        
        .trades-table th {{
            background: #363a45;
            color: #ffffff;
            font-weight: 600;
        }}
        
        .trades-table tr:hover {{
            background: #363a45;
        }}
        
        .trade-profit {{ color: #00d4aa; font-weight: bold; }}
        .trade-loss {{ color: #ff6b6b; font-weight: bold; }}
        .side-buy {{ color: #00d4aa; font-weight: bold; }}
        .side-sell {{ color: #ff6b6b; font-weight: bold; }}
        
        @media (max-width: 768px) {{
            .container {{ padding: 5px; }}
            .metrics-bar {{ flex-direction: column; gap: 10px; }}
            .main-chart {{ height: 300px; }}
            .indicator-chart {{ height: 100px; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 {results.get('symbol', 'BTC_USDT')} Trading Strategy Analysis</h1>
            <div class="subtitle">
                Period: {results.get('start_date', '')} to {results.get('end_date', '')} | 
                Strategy: Optimized Hybrid Pattern | 
                Timeframe: 5M/1H
            </div>
        </div>
        
        <!-- Metrics Bar -->
        <div class="metrics-bar">
            <div class="metric">
                <div class="metric-value {'positive' if total_return > 0 else 'negative' if total_return < 0 else 'neutral'}">{total_return:+.2f}%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric">
                <div class="metric-value neutral">{total_trades}</div>
                <div class="metric-label">Trades</div>
            </div>
            <div class="metric">
                <div class="metric-value {'positive' if win_rate >= 50 else 'negative'}">{win_rate:.1f}%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value {'positive' if final_capital > 1000 else 'negative'}">${final_capital:.2f}</div>
                <div class="metric-label">Capital</div>
            </div>
            <div class="metric">
                <div class="metric-value neutral">{results.get('performance_metrics', {}).get('profit_factor', 0):.2f}</div>
                <div class="metric-label">Profit Factor</div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button class="control-btn active" onclick="showTimeframe('5m')">5M</button>
            <button class="control-btn" onclick="showTimeframe('1h')">1H</button>
            <button class="control-btn" onclick="togglePanel('volume')">Volume</button>
            <button class="control-btn" onclick="togglePanel('macd')">MACD</button>
            <button class="control-btn" onclick="togglePanel('rsi')">RSI</button>
            <button class="control-btn" onclick="toggleEntryExit()">Entry/Exit Lines</button>
        </div>
        
        <!-- Main Price Chart -->
        <div class="chart-container">
            <div class="chart-title">Price Chart with Entry/Exit Signals</div>
            <div id="priceChart" class="main-chart"></div>
        </div>
        
        <!-- Volume Chart -->
        <div class="chart-container" id="volumePanel" style="display: none;">
            <div class="chart-title">Volume</div>
            <div id="volumeChart" class="indicator-chart"></div>
        </div>
        
        <!-- MACD Chart -->
        <div class="chart-container" id="macdPanel" style="display: none;">
            <div class="chart-title">MACD</div>
            <div id="macdChart" class="indicator-chart"></div>
        </div>
        
        <!-- RSI Chart -->
        <div class="chart-container" id="rsiPanel" style="display: none;">
            <div class="chart-title">RSI</div>
            <div id="rsiChart" class="indicator-chart"></div>
        </div>
        
        <!-- Trades Table -->
        <div class="trades-section">
            <div class="chart-title">📋 Trade Execution Log</div>
            <table class="trades-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Entry Time</th>
                        <th>Side</th>
                        <th>Pattern</th>
                        <th>Entry Price</th>
                        <th>Exit Price</th>
                        <th>Stop Loss</th>
                        <th>Take Profit</th>
                        <th>Exit Reason</th>
                        <th>PnL</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody id="tradesTableBody">
                    <!-- Populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Data from Python
        const data5m = {df_5m_json};
        const data1h = {df_1h_json};
        const trades = {trades_json};
        
        let currentTimeframe = '5m';
        let visiblePanels = new Set();
        let showEntryExitLines = false;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {{
            createPriceChart();
            populateTradesTable();
        }});
        
        function showTimeframe(timeframe) {{
            currentTimeframe = timeframe;
            document.querySelectorAll('.control-btn').forEach(btn => {{
                if (btn.textContent === '5M' || btn.textContent === '1H') {{
                    btn.classList.remove('active');
                }}
            }});
            event.target.classList.add('active');
            updateAllCharts();
        }}
        
        function togglePanel(panel) {{
            const panelElement = document.getElementById(panel + 'Panel');
            if (visiblePanels.has(panel)) {{
                visiblePanels.delete(panel);
                panelElement.style.display = 'none';
                event.target.classList.remove('active');
            }} else {{
                visiblePanels.add(panel);
                panelElement.style.display = 'block';
                event.target.classList.add('active');
                createIndicatorChart(panel);
            }}
        }}
        
        function toggleEntryExit() {{
            showEntryExitLines = !showEntryExitLines;
            event.target.classList.toggle('active');
            createPriceChart();
        }}
        
        function updateAllCharts() {{
            createPriceChart();
            visiblePanels.forEach(panel => createIndicatorChart(panel));
        }}
        
        function createPriceChart() {{
            const data = currentTimeframe === '5m' ? data5m : data1h;
            
            const traces = [];
            
            // Candlestick
            traces.push({{
                x: data.map(d => d.timestamp),
                open: data.map(d => d.open),
                high: data.map(d => d.high),
                low: data.map(d => d.low),
                close: data.map(d => d.close),
                type: 'candlestick',
                name: '{results.get('symbol', 'BTC_USDT')}',
                increasing: {{fillcolor: '#00d4aa', line: {{color: '#00d4aa'}}}},
                decreasing: {{fillcolor: '#ff6b6b', line: {{color: '#ff6b6b'}}}}
            }});
            
            // EMAs
            traces.push({{
                x: data.map(d => d.timestamp),
                y: data.map(d => d.ema_50),
                type: 'scatter',
                mode: 'lines',
                name: 'EMA 50',
                line: {{color: '#ffb74d', width: 1.5}},
                opacity: 0.8
            }});
            
            traces.push({{
                x: data.map(d => d.timestamp),
                y: data.map(d => d.ema_200),
                type: 'scatter',
                mode: 'lines',
                name: 'EMA 200',
                line: {{color: '#ba68c8', width: 1.5}},
                opacity: 0.8
            }});
            
            // Entry markers
            const buyTrades = trades.filter(t => t.side === 'BUY');
            const sellTrades = trades.filter(t => t.side === 'SELL');
            
            if (buyTrades.length > 0) {{
                traces.push({{
                    x: buyTrades.map(t => t.entry_timestamp),
                    y: buyTrades.map(t => t.entry_price),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'BUY Entry',
                    marker: {{
                        color: '#00d4aa',
                        size: 12,
                        symbol: 'triangle-up',
                        line: {{color: '#ffffff', width: 2}}
                    }}
                }});
            }}
            
            if (sellTrades.length > 0) {{
                traces.push({{
                    x: sellTrades.map(t => t.entry_timestamp),
                    y: sellTrades.map(t => t.entry_price),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'SELL Entry',
                    marker: {{
                        color: '#ff6b6b',
                        size: 12,
                        symbol: 'triangle-down',
                        line: {{color: '#ffffff', width: 2}}
                    }}
                }});
            }}
            
            // Exit markers
            trades.forEach(trade => {{
                if (trade.exit_timestamp && trade.exit_price) {{
                    traces.push({{
                        x: [trade.exit_timestamp],
                        y: [trade.exit_price],
                        type: 'scatter',
                        mode: 'markers',
                        name: `Exit #${{trade.id}}`,
                        marker: {{
                            color: trade.pnl > 0 ? '#00d4aa' : '#ff6b6b',
                            size: 10,
                            symbol: 'x',
                            line: {{color: '#ffffff', width: 2}}
                        }},
                        showlegend: false
                    }});
                }}
            }});
            
            // Entry/Exit lines
            if (showEntryExitLines) {{
                trades.forEach(trade => {{
                    if (trade.exit_timestamp) {{
                        // Entry to Exit line
                        traces.push({{
                            x: [trade.entry_timestamp, trade.exit_timestamp],
                            y: [trade.entry_price, trade.exit_price],
                            type: 'scatter',
                            mode: 'lines',
                            name: `Trade #${{trade.id}}`,
                            line: {{
                                color: trade.pnl > 0 ? '#00d4aa' : '#ff6b6b',
                                width: 2,
                                dash: 'dot'
                            }},
                            showlegend: false
                        }});
                        
                        // Stop loss line
                        traces.push({{
                            x: [trade.entry_timestamp, trade.exit_timestamp],
                            y: [trade.stop_loss, trade.stop_loss],
                            type: 'scatter',
                            mode: 'lines',
                            name: `SL #${{trade.id}}`,
                            line: {{
                                color: '#ff6b6b',
                                width: 1,
                                dash: 'dash'
                            }},
                            opacity: 0.6,
                            showlegend: false
                        }});
                        
                        // Take profit line
                        traces.push({{
                            x: [trade.entry_timestamp, trade.exit_timestamp],
                            y: [trade.take_profit_1, trade.take_profit_1],
                            type: 'scatter',
                            mode: 'lines',
                            name: `TP #${{trade.id}}`,
                            line: {{
                                color: '#00d4aa',
                                width: 1,
                                dash: 'dash'
                            }},
                            opacity: 0.6,
                            showlegend: false
                        }});
                    }}
                }});
            }}
            
            const layout = {{
                plot_bgcolor: '#1e222d',
                paper_bgcolor: '#2a2e39',
                font: {{color: '#d1d4dc', size: 11}},
                margin: {{l: 60, r: 60, t: 30, b: 40}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true,
                    rangeslider: {{visible: false}}
                }},
                yaxis: {{
                    title: 'Price ($)',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: true,
                legend: {{
                    x: 0,
                    y: 1,
                    bgcolor: 'rgba(42, 46, 57, 0.8)',
                    bordercolor: '#363a45',
                    borderwidth: 1
                }},
                hovermode: 'x unified'
            }};
            
            Plotly.newPlot('priceChart', traces, layout, {{responsive: true}});
        }}
        
        function createIndicatorChart(indicator) {{
            const data = currentTimeframe === '5m' ? data5m : data1h;
            const traces = [];
            let layout = {{
                plot_bgcolor: '#1e222d',
                paper_bgcolor: '#2a2e39',
                font: {{color: '#d1d4dc', size: 10}},
                margin: {{l: 60, r: 60, t: 10, b: 30}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                yaxis: {{
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: false,
                hovermode: 'x'
            }};
            
            if (indicator === 'volume') {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.volume),
                    type: 'bar',
                    name: 'Volume',
                    marker: {{color: '#4a4e5a', opacity: 0.7}}
                }});
                layout.yaxis.title = 'Volume';
            }} else if (indicator === 'macd') {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD',
                    line: {{color: '#2196f3', width: 1.5}}
                }});
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd_signal),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Signal',
                    line: {{color: '#ff9800', width: 1.5}}
                }});
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd_histogram),
                    type: 'bar',
                    name: 'Histogram',
                    marker: {{color: '#4a4e5a', opacity: 0.6}}
                }});
                layout.yaxis.title = 'MACD';
            }} else if (indicator === 'rsi') {{
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.rsi),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: {{color: '#9c27b0', width: 2}}
                }});
                // RSI levels
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: Array(data.length).fill(70),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#ff6b6b', width: 1, dash: 'dash'}},
                    opacity: 0.5
                }});
                traces.push({{
                    x: data.map(d => d.timestamp),
                    y: Array(data.length).fill(30),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#00d4aa', width: 1, dash: 'dash'}},
                    opacity: 0.5
                }});
                layout.yaxis.title = 'RSI';
                layout.yaxis.range = [0, 100];
            }}
            
            Plotly.newPlot(indicator + 'Chart', traces, layout, {{responsive: true}});
        }}
        
        function populateTradesTable() {{
            const tbody = document.getElementById('tradesTableBody');
            tbody.innerHTML = '';
            
            if (trades.length === 0) {{
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="11" style="text-align: center; color: #868993; padding: 20px;">No trades executed</td>';
                tbody.appendChild(row);
                return;
            }}
            
            trades.forEach(trade => {{
                const row = document.createElement('tr');
                const pnlClass = trade.pnl > 0 ? 'trade-profit' : 'trade-loss';
                const sideClass = trade.side === 'BUY' ? 'side-buy' : 'side-sell';
                
                row.innerHTML = `
                    <td>${{trade.id}}</td>
                    <td>${{new Date(trade.entry_timestamp).toLocaleString()}}</td>
                    <td class="${{sideClass}}">${{trade.side}}</td>
                    <td>${{trade.pattern}}</td>
                    <td>${{trade.entry_price.toFixed(2)}}</td>
                    <td>${{trade.exit_price.toFixed(2)}}</td>
                    <td>${{trade.stop_loss.toFixed(2)}}</td>
                    <td>${{trade.take_profit_1.toFixed(2)}}</td>
                    <td>${{trade.exit_reason}}</td>
                    <td class="${{pnlClass}}">${{trade.pnl > 0 ? '+' : ''}}${{trade.pnl.toFixed(2)}}</td>
                    <td>${{trade.duration}} min</td>
                `;
                tbody.appendChild(row);
            }});
        }}
    </script>
</body>
</html>
'''
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ TradingView-style presentation created: {filename}")
    return filename

def main():
    """Main function"""
    print("="*80)
    print("📊 CREATING TRADINGVIEW-STYLE PRESENTATION")
    print("="*80)
    
    result = load_backtest_results()
    if not result:
        return
    
    results, backtest_file = result
    
    symbol = results.get('symbol', 'BTC_USDT')
    start_date = results.get('start_date', '2024-12-01')
    end_date = results.get('end_date', '2024-12-31')
    trades = results.get('trades', [])
    
    print(f"📊 Creating TradingView-style presentation:")
    print(f"   Symbol: {symbol}")
    print(f"   Period: {start_date} to {end_date}")
    print(f"   Trades: {results.get('total_trades', 0)}")
    print(f"   Return: {results.get('total_return', 0):.2f}%")
    
    df_5m, df_1h = create_synthetic_market_data(start_date, end_date, trades)
    
    if df_5m is None or df_1h is None:
        print("❌ Cannot create presentation without market data")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_filename = f"tradingview_presentation_{symbol}_{timestamp}.html"
    
    created_file = create_tradingview_html(results, df_5m, df_1h, html_filename)
    
    if created_file:
        print(f"\n🎉 TRADINGVIEW-STYLE PRESENTATION CREATED!")
        print(f"📁 File: {created_file}")
        print(f"\n📊 Features:")
        print(f"   ✅ Dark theme like TradingView")
        print(f"   ✅ Main price chart with candlesticks")
        print(f"   ✅ Entry/exit markers with triangles and X marks")
        print(f"   ✅ Stop loss and take profit lines (toggle)")
        print(f"   ✅ Separate panels for Volume, MACD, RSI")
        print(f"   ✅ EMA 50 & 200 overlays")
        print(f"   ✅ 5M/1H timeframe switching")
        print(f"   ✅ Professional metrics bar")
        print(f"   ✅ Detailed trade execution log")
        
        try:
            import webbrowser
            full_path = os.path.abspath(created_file)
            webbrowser.open(f'file://{full_path}')
            print(f"🚀 Opening in browser...")
        except:
            print(f"💡 Open manually: {os.path.abspath(created_file)}")

if __name__ == "__main__":
    main()
