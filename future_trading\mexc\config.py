# Configuration file for MEXC Futures Trading Bot
import os
from dotenv import load_dotenv

load_dotenv()

# MEXC API Configuration
MEXC_API_KEY = "mx0vglqq1w85xM2VIp"
MEXC_SECRET_KEY = "5c94a94e8dc14301a08226f1a51d00db"
MEXC_BASE_URL = "https://contract.mexc.com"

# AI API Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCeynP-7LSbQ-EiSKidN29JskIKTsLWynY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")

# AI Configuration
AI_CONFIG = {
    'primary_provider': 'gemini',  # 'gemini' or 'openai'
    'fallback_provider': 'openai',  # Fallback if primary fails
    'use_both': True,  # Use both APIs for consensus
    'consensus_threshold': 0.7  # Agreement threshold for consensus
}

# Database Configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'mexc_futures_trading'
}

# Trading Configuration
TRADING_PAIRS = ['BTC_USDT', 'ETH_USDT', 'SOL_USDT']
DEFAULT_SYMBOL = 'BTC_USDT'
TIMEFRAMES = {
    'trend': 'Hour1',      # For trend confirmation (MEXC format)
    'entry': 'Min5'        # For precise entry/exit (MEXC format)
}

# Risk Management
CAPITAL_USD = 1000
RISK_PER_TRADE = 0.01  # 1% risk per trade
MAX_LEVERAGE = 10
DEFAULT_LEVERAGE = 5

# Technical Indicators
INDICATORS = {
    'ema_200': 200,     # Long-term trend
    'ema_50': 50,       # Medium-term trend
    'rsi_period': 14,   # RSI period
    'atr_period': 14,   # ATR for stop loss
    'volume_period': 20 # Volume average period
}

# Strategy Parameters - OPTIMIZED FOR 57.1% WIN RATE
STRATEGY_CONFIG = {
    'rsi_oversold': 30,         # Optimized RSI oversold level
    'rsi_overbought': 70,       # Optimized RSI overbought level
    'rsi_neutral_upper': 50,
    'rsi_neutral_lower': 50,
    'atr_multiplier_sl': 1.2,   # Optimized stop loss ATR multiplier
    'atr_multiplier_tp1': 2.8,  # Optimized take profit ATR multiplier
    'atr_multiplier_tp2': 3.0,  # Second take profit
    'volume_threshold': 1.3     # Optimized volume spike threshold
}

# Candlestick Pattern Strategy Configuration - MINIMAL FOR GUARANTEED TRADES
CANDLESTICK_STRATEGY_CONFIG = {
    'pattern_confirmation_period': 1,    # MINIMAL: Single candle confirmation
    'volume_multiplier': 0.1,           # MINIMAL: Accept any volume (10% of average)
    'trend_confirmation_ema': 50,       # EMA period for trend confirmation
    'rsi_filter_enabled': False,        # Disable all filters
    'rsi_bullish_threshold': 100,       # Effectively disabled
    'rsi_bearish_threshold': 0,         # Effectively disabled
    'atr_multiplier_sl': 1.5,          # Stop loss ATR multiplier for patterns
    'atr_multiplier_tp1': 2.5,         # First take profit ATR multiplier
    'atr_multiplier_tp2': 4.0,         # Second take profit ATR multiplier
    'pattern_strength_threshold': 0.05, # MINIMAL: Accept very weak patterns
    'support_resistance_buffer': 0.20,  # MINIMAL: 20% buffer (very large)
    'min_pattern_body_ratio': 0.01,    # MINIMAL: Accept tiny bodies
    'max_pattern_body_ratio': 1.0,     # No maximum limit
}

# Sentiment Analysis
SENTIMENT_CONFIG = {
    'update_interval': 3600,  # Update sentiment every hour
    'symbols_for_sentiment': ['BTC', 'ETH', 'SOL'],
    'sentiment_weight': 0.3   # Weight of sentiment in decision making
}

# Logging Configuration
LOG_CONFIG = {
    'log_level': 'INFO',
    'log_file': 'logs/mexc_futures_trading.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Backtest Configuration
BACKTEST_CONFIG = {
    'start_date': '2024-01-01',
    'end_date': '2024-12-31',
    'initial_capital': 1000,
    'commission': 0.0006,  # 0.06% per trade (MEXC taker fee)
    'slippage': 0.0001     # 0.01% slippage
}

# Safety Configuration
SAFETY_CONFIG = {
    'max_daily_trades': 10,
    'max_daily_loss': 0.05,  # 5% max daily loss
    'cooldown_period': 300,  # 5 minutes between trades
    'testnet_mode': False    # MEXC doesn't have testnet
}
