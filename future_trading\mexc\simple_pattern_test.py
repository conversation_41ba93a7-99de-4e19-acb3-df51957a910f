#!/usr/bin/env python3
"""
Simple test of candlestick pattern strategy without API calls
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from candlestick_patterns import CandlestickPatterns
from candlestick_trading_strategy import CandlestickTradingStrategy
from technical_indicators import TechnicalIndicators

def create_simple_test_data():
    """Create simple test data"""
    print("Creating simple test data...")
    
    # Create 100 candles
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5min')
    np.random.seed(42)
    
    base_price = 50000
    data = []
    
    for i in range(len(dates)):
        price = base_price + i * 10 + np.random.normal(0, 50)
        
        # Create normal candle
        open_price = price
        high_price = price + abs(np.random.normal(0, 30))
        low_price = price - abs(np.random.normal(0, 30))
        close_price = price + np.random.normal(0, 20)
        volume = abs(np.random.normal(1000, 200))
        
        # Make sure OHLC is valid
        high_price = max(open_price, high_price, low_price, close_price)
        low_price = min(open_price, high_price, low_price, close_price)
        
        data.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    print(f"Created {len(df)} candles")
    return df

def add_indicators(df):
    """Add technical indicators"""
    print("Adding indicators...")
    
    indicators = TechnicalIndicators()
    
    # Add indicators
    df['ema_50'] = indicators.ema(df['close'], 50)
    df['ema_200'] = indicators.ema(df['close'], 200)
    df['rsi'] = indicators.rsi(df['close'], 14)
    df['atr'] = indicators.atr(df['high'], df['low'], df['close'], 14)
    
    # Add MACD
    macd_data = indicators.macd(df['close'])
    df['macd'] = macd_data['macd']
    df['macd_signal'] = macd_data['signal']
    df['macd_histogram'] = macd_data['histogram']
    
    # Add volume indicators
    df['volume_sma'] = indicators.volume_sma(df['volume'], 20)
    df['volume_ratio'] = indicators.volume_ratio(df['volume'], 20)
    
    print("Indicators added")
    return df

def test_pattern_detection():
    """Test pattern detection"""
    print("\n=== TESTING PATTERN DETECTION ===")
    
    df = create_simple_test_data()
    df = add_indicators(df)
    
    patterns = CandlestickPatterns()
    
    # Test on last few candles
    for i in range(-5, 0):
        print(f"\nTesting index {i}:")
        all_patterns = patterns.detect_all_patterns(df, i)
        
        if all_patterns['has_pattern']:
            strongest = all_patterns['strongest_pattern']
            print(f"  Pattern found: {strongest['name']} ({strongest['signal']}) - strength: {strongest['strength']:.3f}")
        else:
            print(f"  No patterns detected")

def test_strategy():
    """Test strategy signal generation"""
    print("\n=== TESTING STRATEGY ===")
    
    # Create data for both timeframes
    df_5m = create_simple_test_data()
    df_5m = add_indicators(df_5m)
    
    # Create 1H data (sample every 12 candles)
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    df_1h = add_indicators(df_1h)
    
    strategy = CandlestickTradingStrategy()
    
    print(f"5M data: {len(df_5m)} candles")
    print(f"1H data: {len(df_1h)} candles")
    
    # Test signal generation
    signal_data = strategy.generate_trading_signal(df_1h, df_5m, 'BTC_USDT')
    
    print(f"\nSignal: {signal_data.get('signal', 'None')}")
    print(f"Confidence: {signal_data.get('confidence', 0):.3f}")
    print(f"Reason: {signal_data.get('reason', 'N/A')}")
    
    if signal_data.get('levels'):
        levels = signal_data['levels']
        print(f"Entry: ${levels.get('entry_price', 0):.2f}")
        print(f"Stop Loss: ${levels.get('stop_loss', 0):.2f}")
        print(f"Take Profit 1: ${levels.get('take_profit_1', 0):.2f}")

def test_market_conditions():
    """Test market condition validation"""
    print("\n=== TESTING MARKET CONDITIONS ===")
    
    df_5m = create_simple_test_data()
    df_5m = add_indicators(df_5m)
    
    df_1h = df_5m.iloc[::12].copy().reset_index(drop=True)
    df_1h = add_indicators(df_1h)
    
    strategy = CandlestickTradingStrategy()
    
    conditions = strategy.validate_market_conditions(df_1h, df_5m)
    
    print(f"Market conditions valid: {conditions.get('valid', False)}")
    if conditions.get('valid'):
        print(f"  Trend: {conditions.get('trend_direction', 'unknown')}")
        print(f"  Strength: {conditions.get('trend_strength', 0):.3f}")
        print(f"  Phase: {conditions.get('market_phase', 'unknown')}")
    else:
        print(f"  Reason: {conditions.get('reason', 'unknown')}")

def main():
    """Run all tests"""
    print("="*60)
    print("SIMPLE CANDLESTICK PATTERN STRATEGY TEST")
    print("="*60)
    
    try:
        test_pattern_detection()
        test_strategy()
        test_market_conditions()
        
        print("\n" + "="*60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
