# Advanced Candlestick Pattern Detection for Trading Strategy
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from config import CANDLESTICK_STRATEGY_CONFIG
from logging_config import get_logger

class CandlestickPatterns:
    def __init__(self):
        self.logger = get_logger(__name__)
        self.config = CANDLESTICK_STRATEGY_CONFIG
        
    def calculate_pattern_strength(self, df: pd.DataFrame, pattern_index: int) -> float:
        """Calculate the strength of a candlestick pattern - SIMPLIFIED VERSION"""
        try:
            if pattern_index < 0 or pattern_index >= len(df):
                return 0.0

            current = df.iloc[pattern_index]

            # SIMPLIFIED: Just use basic pattern characteristics
            body_size = abs(current['close'] - current['open'])
            candle_range = current['high'] - current['low']

            if candle_range == 0:
                return 0.0

            # Basic strength calculation (much simpler)
            body_ratio = body_size / candle_range

            # Volume boost (if available)
            volume_boost = 1.0
            if 'volume_ratio' in df.columns:
                volume_ratio = current.get('volume_ratio', 1.0)
                volume_boost = min(volume_ratio, 2.0) / 2.0

            # Simple strength calculation
            base_strength = 0.3 + (body_ratio * 0.4) + (volume_boost * 0.3)

            return min(base_strength, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating pattern strength: {e}")
            return 0.3  # Return default strength instead of 0
    
    def detect_hammer(self, df: pd.DataFrame, index: int = -1) -> Dict[str, any]:
        """Detect Hammer pattern - bullish reversal"""
        try:
            if len(df) < 2:
                return {'detected': False, 'strength': 0, 'type': 'hammer'}
                
            current = df.iloc[index]
            
            # Calculate shadows and body
            body_size = abs(current['close'] - current['open'])
            upper_shadow = current['high'] - max(current['open'], current['close'])
            lower_shadow = min(current['open'], current['close']) - current['low']
            total_range = current['high'] - current['low']
            
            # SIMPLIFIED Hammer criteria (much more lenient)
            is_hammer = (
                lower_shadow > body_size and  # Lower shadow longer than body (was 2x)
                upper_shadow < body_size and  # Upper shadow smaller than body
                total_range > 0 and
                body_size > 0  # Just need some body
            )
            
            strength = 0
            if is_hammer:
                strength = self.calculate_pattern_strength(df, index)
                # SIMPLIFIED: Remove trend validation that reduces strength
                        
            return {
                'detected': is_hammer,  # SIMPLIFIED: Remove strength threshold check here
                'strength': strength,
                'type': 'hammer',
                'signal': 'bullish',
                'body_ratio': body_size / total_range if total_range > 0 else 0,
                'lower_shadow_ratio': lower_shadow / total_range if total_range > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting hammer pattern: {e}")
            return {'detected': False, 'strength': 0, 'type': 'hammer'}
    
    def detect_shooting_star(self, df: pd.DataFrame, index: int = -1) -> Dict[str, any]:
        """Detect Shooting Star pattern - bearish reversal"""
        try:
            if len(df) < 2:
                return {'detected': False, 'strength': 0, 'type': 'shooting_star'}
                
            current = df.iloc[index]
            
            # Calculate shadows and body
            body_size = abs(current['close'] - current['open'])
            upper_shadow = current['high'] - max(current['open'], current['close'])
            lower_shadow = min(current['open'], current['close']) - current['low']
            total_range = current['high'] - current['low']
            
            # SIMPLIFIED Shooting star criteria
            is_shooting_star = (
                upper_shadow > body_size and  # Upper shadow longer than body (was 2x)
                lower_shadow < body_size and  # Lower shadow smaller than body
                total_range > 0 and
                body_size > 0  # Just need some body
            )

            strength = 0
            if is_shooting_star:
                strength = self.calculate_pattern_strength(df, index)
                # SIMPLIFIED: Remove trend validation
                        
            return {
                'detected': is_shooting_star,  # SIMPLIFIED: Remove strength threshold check
                'strength': strength,
                'type': 'shooting_star',
                'signal': 'bearish',
                'body_ratio': body_size / total_range if total_range > 0 else 0,
                'upper_shadow_ratio': upper_shadow / total_range if total_range > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting shooting star pattern: {e}")
            return {'detected': False, 'strength': 0, 'type': 'shooting_star'}
    
    def detect_doji(self, df: pd.DataFrame, index: int = -1) -> Dict[str, any]:
        """Detect Doji pattern - indecision/reversal"""
        try:
            if len(df) < 1:
                return {'detected': False, 'strength': 0, 'type': 'doji'}
                
            current = df.iloc[index]
            
            # Calculate body and total range
            body_size = abs(current['close'] - current['open'])
            total_range = current['high'] - current['low']
            
            # Doji criteria - very small body relative to range
            is_doji = (
                body_size <= total_range * 0.1 and  # Body is less than 10% of total range
                total_range > 0
            )
            
            strength = 0
            doji_type = 'standard'
            
            if is_doji:
                strength = self.calculate_pattern_strength(df, index)
                
                # Determine doji type
                upper_shadow = current['high'] - max(current['open'], current['close'])
                lower_shadow = min(current['open'], current['close']) - current['low']
                
                if upper_shadow > 2 * lower_shadow:
                    doji_type = 'dragonfly'  # Bullish
                elif lower_shadow > 2 * upper_shadow:
                    doji_type = 'gravestone'  # Bearish
                else:
                    doji_type = 'standard'  # Neutral
                    
            return {
                'detected': is_doji and strength >= self.config['pattern_strength_threshold'],
                'strength': strength,
                'type': 'doji',
                'subtype': doji_type,
                'signal': 'bullish' if doji_type == 'dragonfly' else 'bearish' if doji_type == 'gravestone' else 'neutral',
                'body_ratio': body_size / total_range if total_range > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting doji pattern: {e}")
            return {'detected': False, 'strength': 0, 'type': 'doji'}
    
    def detect_engulfing(self, df: pd.DataFrame, index: int = -1) -> Dict[str, any]:
        """Detect Bullish/Bearish Engulfing patterns"""
        try:
            if len(df) < 2:
                return {'detected': False, 'strength': 0, 'type': 'engulfing'}
                
            current = df.iloc[index]
            previous = df.iloc[index-1]
            
            # Bullish Engulfing
            bullish_engulfing = (
                previous['close'] < previous['open'] and  # Previous red candle
                current['close'] > current['open'] and   # Current green candle
                current['open'] < previous['close'] and  # Current opens below previous close
                current['close'] > previous['open']      # Current closes above previous open
            )
            
            # Bearish Engulfing
            bearish_engulfing = (
                previous['close'] > previous['open'] and  # Previous green candle
                current['close'] < current['open'] and   # Current red candle
                current['open'] > previous['close'] and  # Current opens above previous close
                current['close'] < previous['open']      # Current closes below previous open
            )
            
            is_engulfing = bullish_engulfing or bearish_engulfing
            signal = 'bullish' if bullish_engulfing else 'bearish' if bearish_engulfing else 'none'
            
            strength = 0
            if is_engulfing:
                strength = self.calculate_pattern_strength(df, index)
                
                # Additional validation - volume should be higher
                if current['volume'] <= previous['volume']:
                    strength *= 0.8
                    
            return {
                'detected': is_engulfing and strength >= self.config['pattern_strength_threshold'],
                'strength': strength,
                'type': 'engulfing',
                'signal': signal,
                'bullish': bullish_engulfing,
                'bearish': bearish_engulfing
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting engulfing pattern: {e}")
            return {'detected': False, 'strength': 0, 'type': 'engulfing'}
    
    def detect_all_patterns(self, df: pd.DataFrame, index: int = -1) -> Dict[str, any]:
        """Detect all candlestick patterns and return the strongest one"""
        try:
            patterns = {
                'hammer': self.detect_hammer(df, index),
                'shooting_star': self.detect_shooting_star(df, index),
                'doji': self.detect_doji(df, index),
                'engulfing': self.detect_engulfing(df, index)
            }
            
            # Find the strongest detected pattern
            strongest_pattern = None
            max_strength = 0
            
            for pattern_name, pattern_data in patterns.items():
                if pattern_data['detected'] and pattern_data['strength'] > max_strength:
                    max_strength = pattern_data['strength']
                    strongest_pattern = pattern_data
                    strongest_pattern['name'] = pattern_name
            
            return {
                'patterns': patterns,
                'strongest_pattern': strongest_pattern,
                'has_pattern': strongest_pattern is not None,
                'max_strength': max_strength
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting all patterns: {e}")
            return {
                'patterns': {},
                'strongest_pattern': None,
                'has_pattern': False,
                'max_strength': 0
            }
