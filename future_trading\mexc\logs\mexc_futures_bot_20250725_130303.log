2025-07-25 13:03:03 - root - INFO - setup_logging:68 - ================================================================================
2025-07-25 13:03:03 - root - INFO - setup_logging:69 - MEXC FUTURES TRADING BOT - NEW SESSION STARTED
2025-07-25 13:03:03 - root - INFO - setup_logging:70 - Log file: logs\mexc_futures_bot_20250725_130303.log
2025-07-25 13:03:03 - root - INFO - setup_logging:71 - Log level: INFO
2025-07-25 13:03:03 - root - INFO - setup_logging:72 - ================================================================================
2025-07-25 13:03:03 - __main__ - INFO - main:168 - ================================================================================
2025-07-25 13:03:03 - __main__ - INFO - main:169 - QUICK TEST CANDLESTICK PATTERN BACKTEST
2025-07-25 13:03:03 - __main__ - INFO - main:170 - ================================================================================
2025-07-25 13:03:03 - __main__ - INFO - main:171 - Using mock data for faster testing
2025-07-25 13:03:03 - __main__ - INFO - main:172 - Strategy Config: {'pattern_confirmation_period': 3, 'volume_multiplier': 1.5, 'trend_confirmation_ema': 50, 'rsi_filter_enabled': True, 'rsi_bullish_threshold': 40, 'rsi_bearish_threshold': 60, 'atr_multiplier_sl': 1.5, 'atr_multiplier_tp1': 2.5, 'atr_multiplier_tp2': 4.0, 'pattern_strength_threshold': 0.7, 'support_resistance_buffer': 0.002, 'min_pattern_body_ratio': 0.3, 'max_pattern_body_ratio': 0.8}
2025-07-25 13:03:03 - __main__ - INFO - main:173 - ================================================================================
2025-07-25 13:03:03 - __main__ - INFO - main:180 - Testing BTC_USDT from 2024-06-01 to 2024-06-07
2025-07-25 13:03:03 - mexc_api - INFO - __init__:21 - Initializing MEXC API client
2025-07-25 13:03:03 - mexc_api - INFO - __init__:22 - Base URL: https://contract.mexc.com
2025-07-25 13:03:03 - mexc_api - INFO - __init__:23 - API Key: mx0vglqq1w...2VIp
2025-07-25 13:03:03 - mexc_api - INFO - __init__:31 - MEXC API client initialized successfully
2025-07-25 13:03:03 - database - INFO - __init__:13 - DatabaseManager initialized
2025-07-25 13:03:03 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 13:03:03 - technical_indicators - INFO - __init__:17 - TechnicalIndicators initialized (TA library available: False)
2025-07-25 13:03:03 - candlestick_trading_strategy - INFO - __init__:23 - Candlestick Trading Strategy initialized
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - __init__:36 - Candlestick Backtest Engine initialized
2025-07-25 13:03:03 - __main__ - INFO - main:186 - Starting backtest with mock data...
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - run_backtest:356 - Starting candlestick pattern backtest for BTC_USDT from 2024-06-01 to 2024-06-07
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - run_backtest:363 - Fetching 1H data for trend analysis...
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - fetch_historical_data:100 - Using mock data for BTC_USDT Hour1
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - fetch_historical_data:157 - Generated 145 mock candles for Hour1
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - run_backtest:366 - Fetching 5M data for pattern detection...
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - fetch_historical_data:100 - Using mock data for BTC_USDT Min5
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - fetch_historical_data:157 - Generated 1729 mock candles for Min5
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - run_backtest:373 - Adding technical indicators...
2025-07-25 13:03:03 - candlestick_backtest_engine - INFO - run_backtest:385 - Running backtest simulation...
2025-07-25 13:03:05 - candlestick_backtest_engine - INFO - run_backtest:482 - Backtest completed. Results saved to candlestick_backtest_BTC_USDT_20250725_130305.json
2025-07-25 13:03:05 - candlestick_backtest_engine - INFO - run_backtest:483 - Total Return: 0.00%
2025-07-25 13:03:05 - candlestick_backtest_engine - INFO - run_backtest:484 - Total Trades: 0
2025-07-25 13:03:05 - candlestick_backtest_engine - INFO - run_backtest:485 - Win Rate: 0.00%
2025-07-25 13:03:05 - __main__ - INFO - main:194 - 
============================================================
2025-07-25 13:03:05 - __main__ - INFO - main:195 - QUICK TEST RESULTS
2025-07-25 13:03:05 - __main__ - INFO - main:196 - ============================================================
2025-07-25 13:03:05 - __main__ - INFO - main:198 - Symbol: BTC_USDT
2025-07-25 13:03:05 - __main__ - INFO - main:199 - Period: 2024-06-01 to 2024-06-07
2025-07-25 13:03:05 - __main__ - INFO - main:200 - Initial Capital: $1,000.00
2025-07-25 13:03:05 - __main__ - INFO - main:201 - Final Capital: $1,000.00
2025-07-25 13:03:05 - __main__ - INFO - main:202 - Total Return: 0.00%
2025-07-25 13:03:05 - __main__ - INFO - main:203 - Total Trades: 0
2025-07-25 13:03:05 - __main__ - INFO - main:207 - 
PERFORMANCE METRICS:
2025-07-25 13:03:05 - __main__ - INFO - main:208 - Win Rate: 0.00%
2025-07-25 13:03:05 - __main__ - INFO - main:209 - Profit Factor: 0.00
2025-07-25 13:03:05 - __main__ - INFO - main:210 - Average Win: $0.00
2025-07-25 13:03:05 - __main__ - INFO - main:211 - Average Loss: $0.00
2025-07-25 13:03:05 - __main__ - INFO - main:212 - Max Drawdown: 0.00%
2025-07-25 13:03:05 - __main__ - INFO - main:213 - Sharpe Ratio: 0.00
2025-07-25 13:03:05 - __main__ - INFO - main:234 - ============================================================
2025-07-25 13:03:05 - __main__ - INFO - main:235 - Quick test completed successfully!
2025-07-25 13:03:05 - __main__ - INFO - main:236 - If this works well, you can run the full backtest with real data.
