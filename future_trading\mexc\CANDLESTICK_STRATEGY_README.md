# Candlestick Pattern Trading Strategy

## Overview

This is a comprehensive candlestick pattern-based trading strategy designed for MEXC futures trading. The strategy combines advanced pattern recognition, multi-timeframe analysis, and sophisticated risk management to identify high-probability trading opportunities.

## Strategy Components

### 1. Candlestick Pattern Detection (`candlestick_patterns.py`)

The strategy detects and analyzes the following patterns:

#### **<PERSON>** (Bullish Reversal)
- Long lower shadow (≥2x body size)
- Small upper shadow (≤0.5x body size)
- Appears after downtrend
- Best used near support levels

#### **Shooting Star Pattern** (Bearish Reversal)
- Long upper shadow (≥2x body size)
- Small lower shadow (≤0.5x body size)
- Appears after uptrend
- Best used near resistance levels

#### **Do<PERSON>** (Indecision/Reversal)
- Very small body (≤10% of total range)
- Types: Standard, Dragonfly (bullish), Gravestone (bearish)
- Indicates market indecision

#### **Engulfing Patterns** (Strong Reversal/Continuation)
- **Bullish Engulfing**: Green candle completely engulfs previous red candle
- **Bearish Engulfing**: Red candle completely engulfs previous green candle
- Requires volume confirmation

### 2. Pattern Strength Calculation

Each pattern is assigned a strength score (0-1) based on:

- **Volume Strength (30%)**: Current volume vs 20-period average
- **Body Size Strength (25%)**: Body-to-range ratio
- **Trend Position (25%)**: Position within recent trend
- **RSI Divergence (20%)**: RSI extreme levels

### 3. Multi-Timeframe Analysis

#### **1-Hour Timeframe (Trend Analysis)**
- EMA 50/200 for trend direction
- RSI for momentum confirmation
- MACD for trend strength
- Market phase identification (trending/ranging/volatile)

#### **5-Minute Timeframe (Pattern Detection)**
- Precise pattern identification
- Volume spike confirmation
- Support/resistance level analysis
- Entry/exit signal generation

### 4. Market Context Validation

Before executing trades, the strategy validates:

#### **Market Conditions**
- Volatility within acceptable range (0.5% - 5%)
- Sufficient volume (≥50% of average)
- Clear trend direction on 1H timeframe

#### **Support/Resistance Analysis**
- Dynamic level identification using local highs/lows
- Level clustering algorithm
- Pattern proximity validation

#### **RSI Filter** (Optional)
- Bullish patterns: RSI < 40
- Bearish patterns: RSI > 60
- Prevents trading in overbought/oversold extremes

### 5. Risk Management

#### **Position Sizing**
- Fixed percentage risk per trade (default 1%)
- ATR-based stop loss calculation
- Maximum position size limits

#### **Stop Loss & Take Profit**
Pattern-specific multipliers:
- **Hammer/Shooting Star**: Tighter stops (0.8x), higher targets (1.2x)
- **Engulfing**: Standard multipliers
- **Doji**: Conservative approach (1.2x stops, 0.8x targets)

#### **Exit Conditions**
- Stop loss hit
- Take profit levels reached
- Opposite pattern formation
- Trailing stop using EMA50

## Configuration Parameters

### Pattern Detection Settings
```python
CANDLESTICK_STRATEGY_CONFIG = {
    'pattern_confirmation_period': 3,    # Candles for confirmation
    'volume_multiplier': 1.5,           # Volume threshold
    'trend_confirmation_ema': 50,       # EMA for trend
    'rsi_filter_enabled': True,         # Enable RSI filter
    'rsi_bullish_threshold': 40,        # RSI threshold for bullish
    'rsi_bearish_threshold': 60,        # RSI threshold for bearish
    'pattern_strength_threshold': 0.7,  # Minimum pattern strength
    'support_resistance_buffer': 0.002, # S/R proximity buffer
    'min_pattern_body_ratio': 0.3,     # Minimum body ratio
    'max_pattern_body_ratio': 0.8,     # Maximum body ratio
}
```

### Risk Management Settings
```python
'atr_multiplier_sl': 1.5,          # Stop loss multiplier
'atr_multiplier_tp1': 2.5,         # First take profit
'atr_multiplier_tp2': 4.0,         # Second take profit
```

## Files Structure

```
future_trading/mexc/
├── candlestick_patterns.py           # Pattern detection logic
├── candlestick_trading_strategy.py   # Main strategy implementation
├── candlestick_backtest_engine.py    # Backtesting engine
├── run_candlestick_backtest.py       # Backtest launcher
├── test_candlestick_strategy.py      # Strategy testing
└── CANDLESTICK_STRATEGY_README.md    # This documentation
```

## Usage

### 1. Run Basic Backtest
```bash
python run_candlestick_backtest.py --symbol BTC_USDT --start-date 2024-01-01 --end-date 2024-12-31
```

### 2. Run Parameter Optimization
```bash
python run_candlestick_backtest.py --symbol BTC_USDT --optimize
```

### 3. Analyze Pattern Performance
```bash
python run_candlestick_backtest.py --symbol BTC_USDT --analyze-patterns
```

### 4. Test Strategy Components
```bash
python test_candlestick_strategy.py
```

## Expected Performance Characteristics

### Strengths
- **High Win Rate**: Pattern-based strategies typically achieve 60-75% win rates
- **Good Risk/Reward**: 2:1 to 4:1 risk/reward ratios
- **Market Adaptability**: Works in trending and ranging markets
- **Clear Entry/Exit Rules**: Objective pattern-based signals

### Considerations
- **Lower Trade Frequency**: Quality over quantity approach
- **Market Dependent**: Performance varies with market conditions
- **Pattern Reliability**: Some patterns more reliable than others
- **False Signals**: Requires proper filtering and confirmation

## Optimization Guidelines

### Key Parameters to Optimize
1. **Pattern Strength Threshold**: Balance between signal quality and frequency
2. **Volume Multiplier**: Higher values = fewer but higher quality signals
3. **ATR Multipliers**: Adjust based on market volatility
4. **RSI Thresholds**: Fine-tune for different market conditions

### Optimization Process
1. Run parameter sweep across defined ranges
2. Evaluate based on composite score (return + win rate + trade frequency)
3. Validate on out-of-sample data
4. Consider transaction costs and slippage

## Risk Warnings

1. **Backtesting Limitations**: Past performance doesn't guarantee future results
2. **Market Conditions**: Strategy performance varies with market regimes
3. **Overfitting Risk**: Avoid over-optimization on limited data
4. **Transaction Costs**: Include realistic fees and slippage
5. **Position Sizing**: Never risk more than you can afford to lose

## Future Enhancements

1. **Additional Patterns**: Three White Soldiers, Three Black Crows, etc.
2. **Machine Learning**: Pattern strength prediction using ML models
3. **Sentiment Integration**: News and social media sentiment analysis
4. **Dynamic Parameters**: Adaptive parameters based on market conditions
5. **Portfolio Management**: Multi-symbol portfolio optimization

## Support and Maintenance

For questions, issues, or enhancements:
1. Check the test results from `test_candlestick_strategy.py`
2. Review backtest logs for debugging
3. Validate pattern detection with visual inspection
4. Monitor live performance vs backtest results

---

**Disclaimer**: This strategy is for educational and research purposes. Always test thoroughly before using real money and never risk more than you can afford to lose.
