# Candlestick Pattern Strategy Backtesting Engine
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
import json
from mexc_api import MEXCFuturesAPI
from database import DatabaseManager
from technical_indicators import TechnicalIndicators
from candlestick_trading_strategy import CandlestickTradingStrategy
from config import BACKTEST_CONFIG, CANDLESTICK_STRATEGY_CONFIG, CAPITAL_USD
from logging_config import get_logger

class CandlestickBacktestEngine:
    def __init__(self):
        self.logger = get_logger(__name__)
        self.api = MEXCFuturesAPI()
        self.db = DatabaseManager()
        self.indicators = TechnicalIndicators()
        self.strategy = CandlestickTradingStrategy()
        
        # Backtest state
        self.initial_capital = BACKTEST_CONFIG['initial_capital']
        self.current_capital = self.initial_capital
        self.commission = BACKTEST_CONFIG['commission']
        self.slippage = BACKTEST_CONFIG['slippage']
        
        # Trade tracking
        self.trades = []
        self.positions = {}
        self.equity_curve = []
        self.daily_returns = []
        self.pattern_performance = {}
        
        self.logger.info("Candlestick Backtest Engine initialized")

    def fetch_historical_data(self, symbol: str, interval: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Fetch historical data from MEXC API"""
        try:
            self.logger.info(f"Fetching historical data for {symbol} from {start_time} to {end_time}")
            
            all_data = []
            current_start = start_time
            
            # MEXC API limits data per request, so we need to fetch in chunks
            while current_start < end_time:
                # Calculate end time for this chunk (max 2000 candles)
                if interval == 'Min1':
                    chunk_end = min(current_start + timedelta(hours=33), end_time)
                elif interval == 'Min5':
                    chunk_end = min(current_start + timedelta(days=7), end_time)
                elif interval == 'Min15':
                    chunk_end = min(current_start + timedelta(days=21), end_time)
                elif interval == 'Min30':
                    chunk_end = min(current_start + timedelta(days=42), end_time)
                elif interval == 'Hour1':
                    chunk_end = min(current_start + timedelta(days=83), end_time)
                elif interval == 'Hour4':
                    chunk_end = min(current_start + timedelta(days=333), end_time)
                elif interval == 'Day1':
                    chunk_end = min(current_start + timedelta(days=2000), end_time)
                else:
                    chunk_end = min(current_start + timedelta(days=7), end_time)
                
                # Convert to timestamps (MEXC uses seconds)
                start_timestamp = int(current_start.timestamp())
                end_timestamp = int(chunk_end.timestamp())
                
                self.logger.info(f"Fetching chunk: {current_start} to {chunk_end}")
                
                # Fetch data from MEXC
                kline_data = self.api.get_klines(
                    symbol=symbol,
                    interval=interval,
                    start_time=start_timestamp,
                    end_time=end_timestamp
                )
                
                if not kline_data or 'time' not in kline_data:
                    self.logger.warning(f"No data received for {symbol} chunk {current_start}")
                    current_start = chunk_end
                    continue
                
                # Convert to DataFrame
                chunk_df = self.convert_mexc_kline_data(kline_data)
                if not chunk_df.empty:
                    all_data.append(chunk_df)
                
                current_start = chunk_end
                time.sleep(0.1)  # Rate limiting
            
            if not all_data:
                self.logger.error(f"No historical data found for {symbol}")
                return pd.DataFrame()
            
            # Combine all chunks
            df = pd.concat(all_data, ignore_index=True)
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp').reset_index(drop=True)
            
            self.logger.info(f"Fetched {len(df)} candles for {symbol}")
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()

    def convert_mexc_kline_data(self, kline_data: Dict) -> pd.DataFrame:
        """Convert MEXC kline data to DataFrame"""
        try:
            df = pd.DataFrame({
                'timestamp': pd.to_datetime(kline_data['time'], unit='s'),
                'open': pd.to_numeric(kline_data['open']),
                'high': pd.to_numeric(kline_data['high']),
                'low': pd.to_numeric(kline_data['low']),
                'close': pd.to_numeric(kline_data['close']),
                'volume': pd.to_numeric(kline_data['vol'])
            })
            
            return df.sort_values('timestamp').reset_index(drop=True)
            
        except Exception as e:
            self.logger.error(f"Error converting kline data: {e}")
            return pd.DataFrame()

    def prepare_data_with_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to the dataframe"""
        try:
            if df.empty or len(df) < 200:
                return df

            # Add EMAs
            df['ema_50'] = self.indicators.ema(df['close'], 50)
            df['ema_200'] = self.indicators.ema(df['close'], 200)

            # Add RSI
            df['rsi'] = self.indicators.rsi(df['close'], 14)

            # Add ATR
            df['atr'] = self.indicators.atr(df['high'], df['low'], df['close'], 14)

            # Add MACD
            macd_data = self.indicators.macd(df['close'])
            df['macd'] = macd_data['macd']
            df['macd_signal'] = macd_data['signal']
            df['macd_histogram'] = macd_data['histogram']

            # Add volume indicators
            df['volume_sma'] = self.indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = self.indicators.volume_ratio(df['volume'], 20)

            return df

        except Exception as e:
            self.logger.error(f"Error preparing data with indicators: {e}")
            return df

    def execute_trade(self, signal_data: Dict, current_time: datetime) -> Optional[Dict]:
        """Execute a trade based on signal data"""
        try:
            if not self.strategy.validate_signal(signal_data):
                return None
            
            signal = signal_data['signal']
            levels = signal_data['levels']
            entry_price = levels['entry_price']
            position_size = levels['position_size']
            
            # Apply slippage
            if signal == 'BUY':
                actual_entry = entry_price * (1 + self.slippage)
            else:
                actual_entry = entry_price * (1 - self.slippage)
            
            # Calculate trade value and commission
            trade_value = actual_entry * position_size
            commission_cost = trade_value * self.commission
            
            # Check if we have enough capital
            if trade_value + commission_cost > self.current_capital:
                self.logger.warning(f"Insufficient capital for trade: {trade_value + commission_cost} > {self.current_capital}")
                return None
            
            # Create trade record
            trade = {
                'id': len(self.trades) + 1,
                'symbol': signal_data['symbol'],
                'side': signal,
                'entry_price': actual_entry,
                'quantity': position_size,
                'stop_loss': levels['stop_loss'],
                'take_profit_1': levels['take_profit_1'],
                'take_profit_2': levels.get('take_profit_2'),
                'pattern_type': signal_data.get('pattern_type', 'unknown'),
                'pattern_strength': signal_data.get('pattern_strength', 0),
                'confidence': signal_data['confidence'],
                'entry_time': current_time,
                'commission': commission_cost,
                'status': 'open'
            }
            
            # Update capital
            self.current_capital -= (trade_value + commission_cost)
            
            # Add to positions
            self.positions[trade['id']] = trade
            
            self.logger.info(f"Executed {signal} trade for {signal_data['symbol']} at {actual_entry}")
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
            return None

    def check_exit_conditions(self, position: Dict, current_price: float, current_time: datetime) -> Optional[Dict]:
        """Check if position should be exited"""
        try:
            side = position['side']
            entry_price = position['entry_price']
            stop_loss = position['stop_loss']
            take_profit_1 = position['take_profit_1']
            take_profit_2 = position.get('take_profit_2')
            
            exit_reason = None
            exit_price = current_price
            partial_exit = False
            
            # Apply slippage to exit price
            if side == 'BUY':
                actual_exit = current_price * (1 - self.slippage)
                
                # Check stop loss
                if current_price <= stop_loss:
                    exit_reason = 'stop_loss'
                    exit_price = stop_loss * (1 - self.slippage)
                # Check take profits
                elif current_price >= take_profit_1:
                    if take_profit_2 and current_price < take_profit_2:
                        exit_reason = 'take_profit_1'
                        exit_price = take_profit_1 * (1 - self.slippage)
                        partial_exit = True
                    else:
                        exit_reason = 'take_profit_2' if take_profit_2 else 'take_profit_1'
                        exit_price = (take_profit_2 if take_profit_2 else take_profit_1) * (1 - self.slippage)
                        
            else:  # SELL
                actual_exit = current_price * (1 + self.slippage)
                
                # Check stop loss
                if current_price >= stop_loss:
                    exit_reason = 'stop_loss'
                    exit_price = stop_loss * (1 + self.slippage)
                # Check take profits
                elif current_price <= take_profit_1:
                    if take_profit_2 and current_price > take_profit_2:
                        exit_reason = 'take_profit_1'
                        exit_price = take_profit_1 * (1 + self.slippage)
                        partial_exit = True
                    else:
                        exit_reason = 'take_profit_2' if take_profit_2 else 'take_profit_1'
                        exit_price = (take_profit_2 if take_profit_2 else take_profit_1) * (1 + self.slippage)
            
            if exit_reason:
                # Calculate PnL
                if side == 'BUY':
                    pnl = (exit_price - entry_price) * position['quantity']
                else:
                    pnl = (entry_price - exit_price) * position['quantity']
                
                # Subtract exit commission
                trade_value = exit_price * position['quantity']
                exit_commission = trade_value * self.commission
                pnl -= exit_commission
                
                return {
                    'exit_reason': exit_reason,
                    'exit_price': exit_price,
                    'exit_time': current_time,
                    'pnl': pnl,
                    'exit_commission': exit_commission,
                    'partial_exit': partial_exit
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking exit conditions: {e}")
            return None

    def close_position(self, position_id: int, exit_data: Dict):
        """Close a position and update records"""
        try:
            position = self.positions[position_id]
            
            # Update position with exit data
            position.update(exit_data)
            position['status'] = 'closed'
            
            # Update capital
            trade_value = exit_data['exit_price'] * position['quantity']
            self.current_capital += trade_value - exit_data['exit_commission']
            
            # Add to completed trades
            self.trades.append(position.copy())
            
            # Update pattern performance tracking
            pattern_type = position['pattern_type']
            if pattern_type not in self.pattern_performance:
                self.pattern_performance[pattern_type] = {
                    'trades': 0, 'wins': 0, 'total_pnl': 0, 'avg_pnl': 0
                }
            
            self.pattern_performance[pattern_type]['trades'] += 1
            self.pattern_performance[pattern_type]['total_pnl'] += exit_data['pnl']
            if exit_data['pnl'] > 0:
                self.pattern_performance[pattern_type]['wins'] += 1
            
            # Calculate average PnL
            self.pattern_performance[pattern_type]['avg_pnl'] = (
                self.pattern_performance[pattern_type]['total_pnl'] / 
                self.pattern_performance[pattern_type]['trades']
            )
            
            # Remove from active positions
            del self.positions[position_id]
            
            self.logger.info(f"Closed position {position_id}: {exit_data['exit_reason']} - PnL: {exit_data['pnl']:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")

    def update_equity_curve(self, current_time: datetime):
        """Update equity curve with current portfolio value"""
        try:
            # Calculate unrealized PnL from open positions
            unrealized_pnl = 0
            # Note: In a real backtest, we'd calculate unrealized PnL based on current prices
            # For simplicity, we're just tracking realized PnL
            
            total_equity = self.current_capital + unrealized_pnl
            
            self.equity_curve.append({
                'timestamp': current_time,
                'equity': total_equity,
                'realized_pnl': sum(trade.get('pnl', 0) for trade in self.trades),
                'unrealized_pnl': unrealized_pnl,
                'open_positions': len(self.positions)
            })
            
        except Exception as e:
            self.logger.error(f"Error updating equity curve: {e}")

    def run_backtest(self, symbol: str, start_date: str, end_date: str) -> Dict[str, any]:
        """Run complete backtest for candlestick pattern strategy"""
        try:
            self.logger.info(f"Starting candlestick pattern backtest for {symbol} from {start_date} to {end_date}")

            # Parse dates
            start_time = datetime.strptime(start_date, '%Y-%m-%d')
            end_time = datetime.strptime(end_date, '%Y-%m-%d')

            # Fetch historical data for both timeframes
            self.logger.info("Fetching 1H data for trend analysis...")
            df_1h = self.fetch_historical_data(symbol, 'Hour1', start_time, end_time)

            self.logger.info("Fetching 5M data for pattern detection...")
            df_5m = self.fetch_historical_data(symbol, 'Min5', start_time, end_time)

            if df_1h.empty or df_5m.empty:
                return {'error': 'Failed to fetch historical data'}

            # Prepare data with indicators
            self.logger.info("Adding technical indicators...")
            df_1h = self.prepare_data_with_indicators(df_1h)
            df_5m = self.prepare_data_with_indicators(df_5m)

            # Reset backtest state
            self.current_capital = self.initial_capital
            self.trades = []
            self.positions = {}
            self.equity_curve = []
            self.pattern_performance = {}

            # Run backtest simulation
            self.logger.info("Running backtest simulation...")

            # We'll use 5M data as our main loop, but reference 1H for trend
            for i in range(200, len(df_5m)):  # Start after enough data for indicators
                current_time = df_5m.iloc[i]['timestamp']
                current_price = df_5m.iloc[i]['close']

                # Get corresponding 1H data (find closest timestamp)
                h1_index = self.find_closest_timestamp_index(df_1h, current_time)
                if h1_index < 50:  # Need enough 1H data
                    continue

                # Get data slices for analysis
                df_1h_slice = df_1h.iloc[:h1_index+1].copy()
                df_5m_slice = df_5m.iloc[:i+1].copy()

                # Check exit conditions for open positions
                positions_to_close = []
                for pos_id, position in self.positions.items():
                    exit_data = self.check_exit_conditions(position, current_price, current_time)
                    if exit_data:
                        positions_to_close.append((pos_id, exit_data))

                # Close positions that hit exit conditions
                for pos_id, exit_data in positions_to_close:
                    self.close_position(pos_id, exit_data)

                # Generate new trading signal if no positions or limited positions
                if len(self.positions) < 1:  # Limit to 1 position at a time for pattern trading
                    signal_data = self.strategy.generate_trading_signal(
                        df_1h_slice, df_5m_slice, symbol
                    )

                    if signal_data['signal']:
                        trade = self.execute_trade(signal_data, current_time)
                        if trade:
                            self.logger.info(f"New {signal_data['signal']} signal: {signal_data['pattern_type']} pattern")

                # Update equity curve every 100 candles (for performance)
                if i % 100 == 0:
                    self.update_equity_curve(current_time)

            # Close any remaining open positions at the end
            final_price = df_5m.iloc[-1]['close']
            final_time = df_5m.iloc[-1]['timestamp']

            for pos_id, position in list(self.positions.items()):
                # Force close at market price
                if position['side'] == 'BUY':
                    exit_price = final_price * (1 - self.slippage)
                    pnl = (exit_price - position['entry_price']) * position['quantity']
                else:
                    exit_price = final_price * (1 + self.slippage)
                    pnl = (position['entry_price'] - exit_price) * position['quantity']

                exit_commission = exit_price * position['quantity'] * self.commission
                pnl -= exit_commission

                exit_data = {
                    'exit_reason': 'backtest_end',
                    'exit_price': exit_price,
                    'exit_time': final_time,
                    'pnl': pnl,
                    'exit_commission': exit_commission,
                    'partial_exit': False
                }

                self.close_position(pos_id, exit_data)

            # Final equity update
            self.update_equity_curve(final_time)

            # Calculate performance metrics
            performance = self.calculate_performance_metrics()

            # Save results
            results = {
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date,
                'initial_capital': self.initial_capital,
                'final_capital': self.current_capital,
                'total_return': (self.current_capital - self.initial_capital) / self.initial_capital * 100,
                'total_trades': len(self.trades),
                'performance_metrics': performance,
                'pattern_performance': self.pattern_performance,
                'equity_curve': self.equity_curve,
                'trades': self.trades,
                'strategy_config': CANDLESTICK_STRATEGY_CONFIG,
                'backtest_completed': datetime.now().isoformat()
            }

            # Save to file
            filename = f"candlestick_backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.logger.info(f"Backtest completed. Results saved to {filename}")
            self.logger.info(f"Total Return: {results['total_return']:.2f}%")
            self.logger.info(f"Total Trades: {results['total_trades']}")
            self.logger.info(f"Win Rate: {performance.get('win_rate', 0):.2f}%")

            return results

        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            return {'error': str(e)}

    def find_closest_timestamp_index(self, df: pd.DataFrame, target_time: datetime) -> int:
        """Find the index of the closest timestamp in the dataframe"""
        try:
            # Convert target_time to the same timezone as df if needed
            time_diffs = abs(df['timestamp'] - target_time)
            closest_index = time_diffs.idxmin()
            return closest_index
        except Exception as e:
            self.logger.error(f"Error finding closest timestamp: {e}")
            return 0

    def calculate_performance_metrics(self) -> Dict[str, any]:
        """Calculate comprehensive performance metrics"""
        try:
            if not self.trades:
                return {'error': 'No trades to analyze'}

            # Basic metrics
            total_trades = len(self.trades)
            winning_trades = sum(1 for trade in self.trades if trade.get('pnl', 0) > 0)
            losing_trades = total_trades - winning_trades

            win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0

            # PnL metrics
            total_pnl = sum(trade.get('pnl', 0) for trade in self.trades)
            gross_profit = sum(trade['pnl'] for trade in self.trades if trade.get('pnl', 0) > 0)
            gross_loss = sum(trade['pnl'] for trade in self.trades if trade.get('pnl', 0) < 0)

            avg_win = gross_profit / winning_trades if winning_trades > 0 else 0
            avg_loss = gross_loss / losing_trades if losing_trades > 0 else 0

            profit_factor = abs(gross_profit / gross_loss) if gross_loss != 0 else float('inf')

            # Risk metrics
            returns = [trade.get('pnl', 0) / self.initial_capital for trade in self.trades]

            if len(returns) > 1:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) != 0 else 0
                max_drawdown = self.calculate_max_drawdown()
            else:
                sharpe_ratio = 0
                max_drawdown = 0

            # Pattern-specific metrics
            pattern_summary = {}
            for pattern_type, stats in self.pattern_performance.items():
                pattern_summary[pattern_type] = {
                    'trades': stats['trades'],
                    'win_rate': stats['wins'] / stats['trades'] * 100 if stats['trades'] > 0 else 0,
                    'avg_pnl': stats['avg_pnl'],
                    'total_pnl': stats['total_pnl']
                }

            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 2),
                'total_pnl': round(total_pnl, 2),
                'gross_profit': round(gross_profit, 2),
                'gross_loss': round(gross_loss, 2),
                'avg_win': round(avg_win, 2),
                'avg_loss': round(avg_loss, 2),
                'profit_factor': round(profit_factor, 2),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'max_drawdown': round(max_drawdown, 2),
                'return_percentage': round(total_pnl / self.initial_capital * 100, 2),
                'pattern_performance': pattern_summary
            }

        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {e}")
            return {'error': str(e)}

    def calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from equity curve"""
        try:
            if not self.equity_curve:
                return 0

            equity_values = [point['equity'] for point in self.equity_curve]
            peak = equity_values[0]
            max_dd = 0

            for equity in equity_values:
                if equity > peak:
                    peak = equity

                drawdown = (peak - equity) / peak * 100
                if drawdown > max_dd:
                    max_dd = drawdown

            return max_dd

        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {e}")
            return 0
