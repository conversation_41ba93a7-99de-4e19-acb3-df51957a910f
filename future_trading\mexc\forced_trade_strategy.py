#!/usr/bin/env python3
"""
Forced Trade Strategy - Guarantees trades for testing backtesting infrastructure
This bypasses all pattern detection and generates trades at regular intervals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List

from candlestick_trading_strategy import CandlestickTradingStrategy
from config import CANDLESTICK_STRATEGY_CONFIG, CAPITAL_USD, RISK_PER_TRADE
from logging_config import get_logger

class ForcedTradeStrategy(CandlestickTradingStrategy):
    """Strategy that forces trades at regular intervals for testing"""
    
    def __init__(self):
        super().__init__()
        self.trade_counter = 0
        self.last_trade_index = 0
        self.trade_interval = 100  # Force a trade every 100 candles
        
        self.logger.info("Forced Trade Strategy initialized - WILL GENERATE TRADES!")
    
    def generate_trading_signal(self, df_1h: pd.DataFrame, df_5m: pd.DataFrame, 
                              symbol: str) -> Dict[str, any]:
        """Generate forced trading signals at regular intervals"""
        try:
            # Basic data check
            if len(df_1h) < 10 or len(df_5m) < 50:
                return {
                    'signal': None,
                    'confidence': 0,
                    'reason': 'insufficient_data',
                    'timestamp': datetime.now(),
                    'symbol': symbol
                }
            
            current_index = len(df_5m)
            
            # Force a trade every trade_interval candles
            if current_index - self.last_trade_index >= self.trade_interval:
                self.trade_counter += 1
                self.last_trade_index = current_index
                
                # Alternate between BUY and SELL
                signal = 'BUY' if self.trade_counter % 2 == 1 else 'SELL'
                
                # Use current market data
                latest_5m = df_5m.iloc[-1]
                entry_price = latest_5m['close']
                atr = latest_5m.get('atr', entry_price * 0.02)
                
                # Calculate levels
                levels = self.calculate_stop_loss_take_profit(
                    entry_price, signal, atr, 'forced_pattern'
                )
                levels['entry_price'] = entry_price
                levels['position_size'] = self.calculate_position_sizing(
                    entry_price, levels['stop_loss']
                )
                
                self.logger.info(f"🎯 FORCED TRADE #{self.trade_counter}: {signal} at ${entry_price:.2f}")
                
                return {
                    'signal': signal,
                    'confidence': 0.8,  # High confidence for forced trades
                    'pattern_type': 'forced_pattern',
                    'pattern_strength': 0.8,
                    'levels': levels,
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'forced_trade': True,
                    'trade_number': self.trade_counter
                }
            
            # No trade this time
            return {
                'signal': None,
                'confidence': 0,
                'reason': f'waiting_for_interval (next trade in {self.trade_interval - (current_index - self.last_trade_index)} candles)',
                'timestamp': datetime.now(),
                'symbol': symbol
            }
            
        except Exception as e:
            self.logger.error(f"Error in forced trade generation: {e}")
            return {
                'signal': None,
                'confidence': 0,
                'error': str(e),
                'timestamp': datetime.now(),
                'symbol': symbol
            }
    
    def calculate_stop_loss_take_profit(self, entry_price: float, signal: str, 
                                      atr: float, pattern_type: str) -> Dict[str, float]:
        """Calculate stop loss and take profit for forced trades"""
        try:
            # Use standard multipliers
            sl_multiplier = 1.5
            tp1_multiplier = 2.5
            tp2_multiplier = 4.0
            
            if signal == 'BUY':
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit_1 = entry_price + (atr * tp1_multiplier)
                take_profit_2 = entry_price + (atr * tp2_multiplier)
            else:  # SELL
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit_1 = entry_price - (atr * tp1_multiplier)
                take_profit_2 = entry_price - (atr * tp2_multiplier)
            
            return {
                'stop_loss': round(stop_loss, 8),
                'take_profit_1': round(take_profit_1, 8),
                'take_profit_2': round(take_profit_2, 8),
                'risk_reward_1': abs(take_profit_1 - entry_price) / abs(entry_price - stop_loss),
                'risk_reward_2': abs(take_profit_2 - entry_price) / abs(entry_price - stop_loss)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating forced trade SL/TP: {e}")
            return {
                'stop_loss': entry_price * 0.98 if signal == 'BUY' else entry_price * 1.02,
                'take_profit_1': entry_price * 1.025 if signal == 'BUY' else entry_price * 0.975,
                'take_profit_2': entry_price * 1.04 if signal == 'BUY' else entry_price * 0.96,
                'risk_reward_1': 1.25,
                'risk_reward_2': 2.0
            }
